import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../core/theme/app_theme.dart';
import '../../core/services/health_worker_notification_service.dart';
import '../../widgets/voice_button.dart';

class HealthWorkerNotificationsScreen extends StatefulWidget {
  const HealthWorkerNotificationsScreen({super.key});

  @override
  State<HealthWorkerNotificationsScreen> createState() => _HealthWorkerNotificationsScreenState();
}

class _HealthWorkerNotificationsScreenState extends State<HealthWorkerNotificationsScreen> {
  final HealthWorkerNotificationService _notificationService = HealthWorkerNotificationService();
  List<HealthWorkerNotification> _notifications = [];
  bool _isLoading = false;
  bool _showUnreadOnly = false;
  int _unreadCount = 0;

  @override
  void initState() {
    super.initState();
    _loadNotifications();
    _loadUnreadCount();
  }

  Future<void> _loadNotifications() async {
    setState(() => _isLoading = true);
    
    try {
      final notifications = await _notificationService.getNotifications(
        unreadOnly: _showUnreadOnly,
      );
      setState(() {
        _notifications = notifications;
      });
    } catch (e) {
      _showErrorSnackBar('Habaye ikosa mu gushaka amamenyo');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadUnreadCount() async {
    try {
      final count = await _notificationService.getUnreadCount();
      setState(() {
        _unreadCount = count;
      });
    } catch (e) {
      // Silently handle error
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.errorColor,
      ),
    );
  }

  void _handleVoiceCommand(String command) {
    final lowerCommand = command.toLowerCase();
    if (lowerCommand.contains('gusoma') || lowerCommand.contains('read')) {
      // Mark all as read
      _markAllAsRead();
    } else if (lowerCommand.contains('hamagara') || lowerCommand.contains('call')) {
      // Find first critical notification and call client
      final criticalNotification = _notifications.firstWhere(
        (n) => n.severity == NotificationSeverity.critical,
        orElse: () => _notifications.isNotEmpty ? _notifications.first : null,
      );
      if (criticalNotification?.clientPhone != null) {
        _makeCall(criticalNotification!.clientPhone!);
      }
    }
  }

  Future<void> _makeCall(String phoneNumber) async {
    final uri = Uri(scheme: 'tel', path: phoneNumber);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      _showErrorSnackBar('Ntibishoboka guhamagara uyu numero');
    }
  }

  Future<void> _markAsRead(HealthWorkerNotification notification) async {
    if (notification.isRead) return;

    try {
      await _notificationService.markAsRead(notification.id);
      await _loadNotifications();
      await _loadUnreadCount();
    } catch (e) {
      _showErrorSnackBar('Habaye ikosa mu gusoma ubutumwa');
    }
  }

  Future<void> _markAllAsRead() async {
    try {
      for (final notification in _notifications.where((n) => !n.isRead)) {
        await _notificationService.markAsRead(notification.id);
      }
      await _loadNotifications();
      await _loadUnreadCount();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Amamenyo yose yasomwe'),
            backgroundColor: AppTheme.successColor,
          ),
        );
      }
    } catch (e) {
      _showErrorSnackBar('Habaye ikosa mu gusoma amamenyo');
    }
  }

  Future<void> _acknowledgeNotification(HealthWorkerNotification notification) async {
    final responseController = TextEditingController();
    
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Emeza ubutumwa'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Emeza ko warabonye ubu butumwa bw\'ihutirwa'),
            SizedBox(height: AppTheme.spacing16),
            TextField(
              controller: responseController,
              decoration: InputDecoration(
                labelText: 'Icyo uzakora (optional)',
                hintText: 'Urugero: Nzamuhamagara vuba...',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('Hagarika'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: AppTheme.primaryColor),
            child: Text('Emeza'),
          ),
        ],
      ),
    );

    if (result == true) {
      try {
        await _notificationService.acknowledgeNotification(
          notification.id,
          responseNote: responseController.text.isNotEmpty ? responseController.text : null,
        );
        await _loadNotifications();
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Ubutumwa bwemejwe'),
              backgroundColor: AppTheme.successColor,
            ),
          );
        }
      } catch (e) {
        _showErrorSnackBar('Habaye ikosa mu kwemeza ubutumwa');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width > 600;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          'Amamenyo y\'abakiriya',
          style: AppTheme.headingMedium.copyWith(
            color: Colors.white,
            fontSize: isTablet ? 24 : 20,
          ),
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          if (_unreadCount > 0)
            Container(
              margin: EdgeInsets.only(right: AppTheme.spacing16),
              padding: EdgeInsets.symmetric(
                horizontal: AppTheme.spacing8,
                vertical: AppTheme.spacing4,
              ),
              decoration: BoxDecoration(
                color: AppTheme.errorColor,
                borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
              ),
              child: Text(
                '$_unreadCount',
                style: AppTheme.bodySmall.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          IconButton(
            icon: Icon(Icons.mark_email_read_rounded),
            onPressed: _markAllAsRead,
            tooltip: 'Soma byose',
          ),
        ],
      ),
      body: Column(
        children: [
          // Filter Toggle
          Container(
            padding: EdgeInsets.all(AppTheme.spacing16),
            child: Row(
              children: [
                Text(
                  'Reba:',
                  style: AppTheme.labelLarge,
                ),
                SizedBox(width: AppTheme.spacing16),
                Expanded(
                  child: SegmentedButton<bool>(
                    segments: [
                      ButtonSegment(
                        value: false,
                        label: Text('Byose'),
                        icon: Icon(Icons.list_rounded),
                      ),
                      ButtonSegment(
                        value: true,
                        label: Text('Bitasomwe ($_unreadCount)'),
                        icon: Icon(Icons.mark_email_unread_rounded),
                      ),
                    ],
                    selected: {_showUnreadOnly},
                    onSelectionChanged: (Set<bool> selection) {
                      setState(() {
                        _showUnreadOnly = selection.first;
                      });
                      _loadNotifications();
                    },
                  ),
                ),
              ],
            ),
          ),

          // Notifications List
          Expanded(
            child: _isLoading
                ? Center(child: CircularProgressIndicator())
                : _notifications.isEmpty
                    ? _buildEmptyState(isTablet)
                    : RefreshIndicator(
                        onRefresh: () async {
                          await _loadNotifications();
                          await _loadUnreadCount();
                        },
                        child: ListView.builder(
                          padding: EdgeInsets.all(AppTheme.spacing16),
                          itemCount: _notifications.length,
                          itemBuilder: (context, index) {
                            final notification = _notifications[index];
                            return _buildNotificationCard(notification, isTablet, index);
                          },
                        ),
                      ),
          ),
        ],
      ),
      floatingActionButton: VoiceButton(
        prompt: 'Vuga icyo ushaka gukora - urugero: "Soma byose" cyangwa "Hamagara umukiriya"',
        onResult: _handleVoiceCommand,
        tooltip: 'Koresha ijwi',
      ),
    );
  }

  Widget _buildEmptyState(bool isTablet) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _showUnreadOnly ? Icons.mark_email_read_rounded : Icons.notifications_none_rounded,
            size: isTablet ? 80 : 64,
            color: AppTheme.textTertiary,
          ),
          SizedBox(height: AppTheme.spacing16),
          Text(
            _showUnreadOnly ? 'Nta menyo itasomwe' : 'Nta menyo',
            style: AppTheme.headingSmall.copyWith(
              color: AppTheme.textTertiary,
            ),
          ),
          SizedBox(height: AppTheme.spacing8),
          Text(
            _showUnreadOnly 
                ? 'Amamenyo yose yasomwe'
                : 'Nta menyo y\'abakiriya ubu',
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationCard(HealthWorkerNotification notification, bool isTablet, int index) {
    final severityColor = _getSeverityColor(notification.severity);
    final severityIcon = _getSeverityIcon(notification.severity);

    return Container(
      margin: EdgeInsets.only(bottom: AppTheme.spacing12),
      decoration: BoxDecoration(
        color: notification.isRead ? AppTheme.surfaceColor : severityColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
        boxShadow: AppTheme.softShadow,
        border: notification.isRead 
            ? null 
            : Border.all(color: severityColor.withValues(alpha: 0.3), width: 2),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _markAsRead(notification),
          borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
          child: Padding(
            padding: EdgeInsets.all(isTablet ? AppTheme.spacing20 : AppTheme.spacing16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with severity and time
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: AppTheme.spacing8,
                        vertical: AppTheme.spacing4,
                      ),
                      decoration: BoxDecoration(
                        color: severityColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(severityIcon, color: severityColor, size: 16),
                          SizedBox(width: AppTheme.spacing4),
                          Text(
                            _getSeverityText(notification.severity),
                            style: AppTheme.bodySmall.copyWith(
                              color: severityColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Spacer(),
                    Text(
                      _formatTime(notification.createdAt),
                      style: AppTheme.bodySmall.copyWith(
                        color: AppTheme.textTertiary,
                      ),
                    ),
                    if (!notification.isRead) ...[
                      SizedBox(width: AppTheme.spacing8),
                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ],
                  ],
                ),
                SizedBox(height: AppTheme.spacing12),

                // Title
                Text(
                  notification.title,
                  style: AppTheme.labelLarge.copyWith(
                    fontWeight: FontWeight.w600,
                    color: notification.isRead ? AppTheme.textSecondary : AppTheme.textPrimary,
                  ),
                ),
                SizedBox(height: AppTheme.spacing8),

                // Message
                Text(
                  notification.message,
                  style: AppTheme.bodyMedium.copyWith(
                    color: notification.isRead ? AppTheme.textTertiary : AppTheme.textSecondary,
                  ),
                ),

                // Metadata (health values, trends)
                if (notification.metadata != null) ...[
                  SizedBox(height: AppTheme.spacing12),
                  _buildMetadataSection(notification.metadata!, isTablet),
                ],

                // Action buttons
                SizedBox(height: AppTheme.spacing16),
                Row(
                  children: [
                    if (notification.clientPhone != null)
                      ElevatedButton.icon(
                        onPressed: () => _makeCall(notification.clientPhone!),
                        icon: Icon(Icons.phone_rounded, size: 16),
                        label: Text('Hamagara'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.successColor,
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(
                            horizontal: AppTheme.spacing12,
                            vertical: AppTheme.spacing8,
                          ),
                        ),
                      ),
                    if (notification.clientPhone != null) SizedBox(width: AppTheme.spacing8),
                    if (!notification.isAcknowledged && 
                        (notification.severity == NotificationSeverity.high || 
                         notification.severity == NotificationSeverity.critical))
                      ElevatedButton.icon(
                        onPressed: () => _acknowledgeNotification(notification),
                        icon: Icon(Icons.check_circle_rounded, size: 16),
                        label: Text('Emeza'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(
                            horizontal: AppTheme.spacing12,
                            vertical: AppTheme.spacing8,
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    ).animate(delay: (index * 100).ms).fadeIn().slideX(begin: 0.3);
  }

  Widget _buildMetadataSection(Map<String, dynamic> metadata, bool isTablet) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing12),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (metadata['weeklyTrend'] != null) ...[
            Row(
              children: [
                Icon(
                  _getTrendIcon(metadata['weeklyTrend']),
                  color: _getTrendColor(metadata['weeklyTrend']),
                  size: 16,
                ),
                SizedBox(width: AppTheme.spacing4),
                Text(
                  'Icyerekezo: ${_getTrendText(metadata['weeklyTrend'])}',
                  style: AppTheme.bodySmall.copyWith(
                    color: _getTrendColor(metadata['weeklyTrend']),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
          if (metadata['dataPoints'] != null) ...[
            SizedBox(height: AppTheme.spacing4),
            Text(
              'Amakuru ${metadata['dataPoints']} mu cyumweru gishize',
              style: AppTheme.bodySmall.copyWith(
                color: AppTheme.textTertiary,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getSeverityColor(NotificationSeverity severity) {
    switch (severity) {
      case NotificationSeverity.critical:
        return AppTheme.errorColor;
      case NotificationSeverity.high:
        return Colors.orange;
      case NotificationSeverity.medium:
        return AppTheme.primaryColor;
      case NotificationSeverity.low:
        return AppTheme.successColor;
    }
  }

  IconData _getSeverityIcon(NotificationSeverity severity) {
    switch (severity) {
      case NotificationSeverity.critical:
        return Icons.emergency_rounded;
      case NotificationSeverity.high:
        return Icons.warning_rounded;
      case NotificationSeverity.medium:
        return Icons.info_rounded;
      case NotificationSeverity.low:
        return Icons.check_circle_rounded;
    }
  }

  String _getSeverityText(NotificationSeverity severity) {
    switch (severity) {
      case NotificationSeverity.critical:
        return 'IHUTIRWA';
      case NotificationSeverity.high:
        return 'BYIHUTIRWA';
      case NotificationSeverity.medium:
        return 'BISANZWE';
      case NotificationSeverity.low:
        return 'BYIZA';
    }
  }

  IconData _getTrendIcon(String trend) {
    switch (trend) {
      case 'INCREASING':
        return Icons.trending_up_rounded;
      case 'DECREASING':
        return Icons.trending_down_rounded;
      default:
        return Icons.trending_flat_rounded;
    }
  }

  Color _getTrendColor(String trend) {
    switch (trend) {
      case 'INCREASING':
        return AppTheme.errorColor;
      case 'DECREASING':
        return AppTheme.successColor;
      default:
        return AppTheme.textSecondary;
    }
  }

  String _getTrendText(String trend) {
    switch (trend) {
      case 'INCREASING':
        return 'Birazamuka';
      case 'DECREASING':
        return 'Biragabanuka';
      default:
        return 'Biracumbitse';
    }
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}min';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h';
    } else {
      return '${difference.inDays}d';
    }
  }
}
