import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/theme/app_theme.dart';
import '../../core/constants/app_constants.dart';
import '../../core/services/education_service.dart';
import '../../core/services/auth_service.dart';
import '../../widgets/voice_button.dart';
import 'lesson_detail_screen.dart';

class EducationScreen extends StatefulWidget {
  const EducationScreen({super.key});

  @override
  State<EducationScreen> createState() => _EducationScreenState();
}

class _EducationScreenState extends State<EducationScreen> {
  String _selectedCategory = 'all';
  final EducationService _educationService = EducationService();
  final AuthService _authService = AuthService();

  List<EducationLesson> _lessons = [];
  List<EducationProgress> _userProgress = [];
  bool _isLoading = true;
  String? _error;

  final List<EducationCategory> _categories = [
    EducationCategory(id: 'all', name: 'Byose', icon: Icons.apps_rounded),
    EducationCategory(
      id: 'FAMILY_PLANNING',
      name: '<PERSON><PERSON> n\'ubwiyunge',
      icon: Icons.family_restroom_rounded,
    ),
    EducationCategory(
      id: 'REPRODUCTIVE_HEALTH',
      name: 'Ubuzima bw\'imyororokere',
      icon: Icons.health_and_safety_rounded,
    ),
    EducationCategory(
      id: 'CONTRACEPTION',
      name: 'Gukumira inda',
      icon: Icons.medical_services_rounded,
    ),
    EducationCategory(
      id: 'PREGNANCY',
      name: 'Inda',
      icon: Icons.pregnant_woman_rounded,
    ),
    EducationCategory(
      id: 'MENSTRUAL_HEALTH',
      name: 'Ubuzima bw\'imihango',
      icon: Icons.calendar_today_rounded,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _loadEducationData();
  }

  Future<void> _loadEducationData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Try to load lessons from API
      final lessons = await _educationService.getEducationLessons(
        category: _selectedCategory == 'all' ? null : _selectedCategory,
      );

      // Load user progress if authenticated
      List<EducationProgress> progress = [];
      final currentUser = _authService.currentUser;
      if (currentUser != null) {
        try {
          progress = await _educationService.getUserProgress(currentUser.id);
        } catch (e) {
          // Progress loading failed, continue with empty progress
          debugPrint('Failed to load user progress: $e');
        }
      }

      setState(() {
        _lessons = lessons;
        _userProgress = progress;
        _isLoading = false;
        _error = null;
      });
    } catch (e) {
      debugPrint('Failed to load education lessons: $e');
      setState(() {
        _isLoading = false;
        _error = 'Habaye ikosa mu gufata amasomo. Gerageza ukundi.';
      });
    }
  }

  void _onCategorySelected(String categoryId) {
    setState(() {
      _selectedCategory = categoryId;
    });
    _loadEducationData();
  }

  // Helper method to get lesson progress
  EducationProgress? _getLessonProgress(String lessonId) {
    try {
      return _userProgress.firstWhere(
        (progress) => progress.lessonId == lessonId,
      );
    } catch (e) {
      return null;
    }
  }

  // Helper method to check if lesson is completed
  bool _isLessonCompleted(String lessonId) {
    final progress = _getLessonProgress(lessonId);
    return progress?.isCompleted ?? false;
  }

  // Helper method to get lesson progress percentage
  int _getLessonProgressPercentage(String lessonId) {
    final progress = _getLessonProgress(lessonId);
    return progress?.progressPercentage.round() ?? 0;
  }

  // Helper method to format duration
  String _formatDuration(int minutes) {
    if (minutes < 60) {
      return '$minutes iminota';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      if (remainingMinutes == 0) {
        return '$hours ${hours == 1 ? 'isaha' : 'amasaha'}';
      } else {
        return '$hours ${hours == 1 ? 'isaha' : 'amasaha'} $remainingMinutes iminota';
      }
    }
  }

  // Helper method to format difficulty
  String _formatDifficulty(String difficulty) {
    switch (difficulty.toUpperCase()) {
      case 'BEGINNER':
        return 'Byoroshye';
      case 'INTERMEDIATE':
        return 'Hagati';
      case 'ADVANCED':
        return 'Bigoye';
      default:
        return difficulty;
    }
  }

  List<EducationLesson> get _filteredLessons {
    if (_selectedCategory == 'all') return _lessons;
    return _lessons
        .where((lesson) => lesson.category == _selectedCategory)
        .toList();
  }

  void _handleVoiceCommand(String command) {
    final lowerCommand = command.toLowerCase();
    if (lowerCommand.contains('kubana') || lowerCommand.contains('family')) {
      _onCategorySelected('FAMILY_PLANNING');
    } else if (lowerCommand.contains('gukumira') ||
        lowerCommand.contains('contraception')) {
      _onCategorySelected('CONTRACEPTION');
    } else if (lowerCommand.contains('ubuzima') ||
        lowerCommand.contains('health')) {
      _onCategorySelected('REPRODUCTIVE_HEALTH');
    } else if (lowerCommand.contains('inda') ||
        lowerCommand.contains('pregnancy')) {
      _onCategorySelected('PREGNANCY');
    } else if (lowerCommand.contains('imihango') ||
        lowerCommand.contains('menstrual')) {
      _onCategorySelected('MENSTRUAL_HEALTH');
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width > 600;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: CustomScrollView(
        physics: const BouncingScrollPhysics(),
        slivers: [
          // Custom App Bar
          _buildAppBar(isTablet),

          // Categories
          SliverToBoxAdapter(child: _buildCategories(isTablet)),

          // Progress Overview
          SliverToBoxAdapter(child: _buildProgressOverview(isTablet)),

          // Content Area (Loading, Error, or Lessons)
          _buildContent(isTablet),

          // Bottom Padding
          SliverToBoxAdapter(child: SizedBox(height: AppTheme.spacing64)),
        ],
      ),
      floatingActionButton: VoiceButton(
        prompt:
            'Vuga: "Kubana" kugira ngo ugere ku masomo y\'umuryango, "Gukumira" kugira ngo ugere ku buryo bwo gukumira inda',
        onResult: _handleVoiceCommand,
        tooltip: 'Shakisha amasomo mu ijwi',
      ),
    );
  }

  Widget _buildAppBar(bool isTablet) {
    return SliverAppBar(
      expandedHeight: isTablet ? 200 : 160,
      floating: false,
      pinned: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: const BoxDecoration(
            gradient: AppTheme.primaryGradient,
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(32),
              bottomRight: Radius.circular(32),
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: EdgeInsets.all(
                isTablet ? AppTheme.spacing32 : AppTheme.spacing24,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Row(
                    children: [
                      Container(
                        width: isTablet ? 60 : 50,
                        height: isTablet ? 60 : 50,
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(
                            isTablet ? 30 : 25,
                          ),
                          border: Border.all(
                            color: Colors.white.withValues(alpha: 0.3),
                            width: 2,
                          ),
                        ),
                        child: Icon(
                          Icons.school_rounded,
                          color: Colors.white,
                          size: isTablet ? 32 : 28,
                        ),
                      ),
                      SizedBox(width: AppTheme.spacing16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Amasomo',
                              style: AppTheme.headingLarge.copyWith(
                                color: Colors.white,
                                fontSize: isTablet ? 32 : 28,
                              ),
                            ),
                            Text(
                              'Iga ku buzima bw\'imyororokere',
                              style: AppTheme.bodyLarge.copyWith(
                                color: Colors.white.withValues(alpha: 0.9),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCategories(bool isTablet) {
    return Container(
      height: isTablet ? 80 : 70,
      margin: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(
          horizontal: isTablet ? AppTheme.spacing32 : AppTheme.spacing24,
        ),
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final category = _categories[index];
          final isSelected = _selectedCategory == category.id;

          return Container(
                margin: EdgeInsets.only(
                  right:
                      index < _categories.length - 1 ? AppTheme.spacing12 : 0,
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () => _onCategorySelected(category.id),
                    borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal:
                            isTablet ? AppTheme.spacing20 : AppTheme.spacing16,
                        vertical:
                            isTablet ? AppTheme.spacing12 : AppTheme.spacing8,
                      ),
                      decoration: BoxDecoration(
                        gradient: isSelected ? AppTheme.primaryGradient : null,
                        color: isSelected ? null : AppTheme.surfaceColor,
                        borderRadius: BorderRadius.circular(
                          AppTheme.radiusLarge,
                        ),
                        border: Border.all(
                          color:
                              isSelected
                                  ? Colors.transparent
                                  : AppTheme.primaryColor.withValues(
                                    alpha: 0.2,
                                  ),
                        ),
                        boxShadow: isSelected ? AppTheme.softShadow : null,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            category.icon,
                            color:
                                isSelected
                                    ? Colors.white
                                    : AppTheme.primaryColor,
                            size: isTablet ? 24 : 20,
                          ),
                          SizedBox(width: AppTheme.spacing8),
                          Text(
                            category.name,
                            style: AppTheme.labelMedium.copyWith(
                              color:
                                  isSelected
                                      ? Colors.white
                                      : AppTheme.primaryColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              )
              .animate(delay: (index * 100).ms)
              .fadeIn()
              .slideX(begin: 0.3, duration: 600.ms);
        },
      ),
    );
  }

  Widget _buildProgressOverview(bool isTablet) {
    final completedLessons =
        _lessons.where((lesson) => _isLessonCompleted(lesson.id)).length;
    final totalLessons = _lessons.length;
    final progressPercentage =
        totalLessons > 0 ? (completedLessons / totalLessons * 100).round() : 0;

    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: isTablet ? AppTheme.spacing32 : AppTheme.spacing24,
        vertical: AppTheme.spacing16,
      ),
      padding: EdgeInsets.all(
        isTablet ? AppTheme.spacing24 : AppTheme.spacing20,
      ),
      decoration: BoxDecoration(
        gradient: AppTheme.secondaryGradient,
        borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
        boxShadow: AppTheme.mediumShadow,
      ),
      child: Row(
        children: [
          Container(
            width: isTablet ? 80 : 70,
            height: isTablet ? 80 : 70,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(isTablet ? 40 : 35),
            ),
            child: Stack(
              alignment: Alignment.center,
              children: [
                CircularProgressIndicator(
                  value: progressPercentage / 100,
                  strokeWidth: 4,
                  backgroundColor: Colors.white.withValues(alpha: 0.3),
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                ),
                Text(
                  '$progressPercentage%',
                  style: AppTheme.labelLarge.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(width: AppTheme.spacing20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Uko ugeze',
                  style: AppTheme.headingSmall.copyWith(
                    color: Colors.white,
                    fontSize: isTablet ? 20 : 18,
                  ),
                ),
                SizedBox(height: AppTheme.spacing4),
                Text(
                  '$completedLessons ku $totalLessons amasomo yarangiye',
                  style: AppTheme.bodyMedium.copyWith(
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
                SizedBox(height: AppTheme.spacing8),
                Text(
                  'Komeza gutuma! Ugiye gutsinda.',
                  style: AppTheme.bodySmall.copyWith(
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ).animate().fadeIn(delay: 400.ms).slideY(begin: 0.3, duration: 800.ms);
  }

  Widget _buildContent(bool isTablet) {
    if (_isLoading) {
      return SliverToBoxAdapter(
        child: SizedBox(
          height: 300,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppTheme.primaryColor,
                  ),
                ),
                SizedBox(height: AppTheme.spacing16),
                Text(
                  'Gufata amasomo y\'ubuzima...',
                  style: AppTheme.bodyLarge.copyWith(color: Colors.grey[600]),
                ),
              ],
            ),
          ),
        ),
      );
    }

    if (_error != null) {
      return SliverToBoxAdapter(
        child: Container(
          margin: EdgeInsets.all(
            isTablet ? AppTheme.spacing32 : AppTheme.spacing24,
          ),
          padding: EdgeInsets.all(AppTheme.spacing24),
          decoration: BoxDecoration(
            color: AppTheme.errorColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: AppTheme.errorColor.withValues(alpha: 0.3),
            ),
          ),
          child: Column(
            children: [
              Icon(
                Icons.error_outline_rounded,
                color: AppTheme.errorColor,
                size: 48,
              ),
              SizedBox(height: AppTheme.spacing16),
              Text(
                _error!,
                style: AppTheme.bodyLarge.copyWith(color: AppTheme.errorColor),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: AppTheme.spacing16),
              ElevatedButton(
                onPressed: _loadEducationData,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: Text('Ongera ugerageze'),
              ),
            ],
          ),
        ),
      );
    }

    if (_filteredLessons.isEmpty) {
      return SliverToBoxAdapter(
        child: Container(
          margin: EdgeInsets.all(
            isTablet ? AppTheme.spacing32 : AppTheme.spacing24,
          ),
          padding: EdgeInsets.all(AppTheme.spacing24),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            boxShadow: AppTheme.softShadow,
          ),
          child: Column(
            children: [
              Icon(Icons.school_outlined, color: Colors.grey[600], size: 48),
              SizedBox(height: AppTheme.spacing16),
              Text(
                'Nta masomo aboneka muri iki cyiciro',
                style: AppTheme.bodyLarge.copyWith(color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: AppTheme.spacing8),
              Text(
                'Hitamo ikindi cyiciro cyangwa ongera ugerageze nyuma',
                style: AppTheme.bodyMedium.copyWith(color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return SliverPadding(
      padding: EdgeInsets.all(
        isTablet ? AppTheme.spacing32 : AppTheme.spacing24,
      ),
      sliver: SliverGrid(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: isTablet ? 2 : 1,
          crossAxisSpacing: AppTheme.spacing16,
          mainAxisSpacing: AppTheme.spacing16,
          childAspectRatio: isTablet ? 1.2 : 1.4,
        ),
        delegate: SliverChildBuilderDelegate((context, index) {
          final lesson = _filteredLessons[index];
          return _buildLessonCard(lesson, isTablet, index);
        }, childCount: _filteredLessons.length),
      ),
    );
  }

  Widget _buildLessonCard(EducationLesson lesson, bool isTablet, int index) {
    return Container(
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
            boxShadow: AppTheme.softShadow,
            border: Border.all(
              color:
                  _isLessonCompleted(lesson.id)
                      ? AppTheme.successColor.withValues(alpha: 0.3)
                      : AppTheme.primaryColor.withValues(alpha: 0.1),
            ),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                Navigator.of(context).push(
                  PageRouteBuilder(
                    pageBuilder:
                        (context, animation, secondaryAnimation) =>
                            LessonDetailScreen(lesson: lesson),
                    transitionsBuilder: (
                      context,
                      animation,
                      secondaryAnimation,
                      child,
                    ) {
                      return SlideTransition(
                        position: Tween<Offset>(
                          begin: const Offset(1.0, 0.0),
                          end: Offset.zero,
                        ).animate(animation),
                        child: child,
                      );
                    },
                    transitionDuration: AppConstants.mediumAnimation,
                  ),
                );
              },
              borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
              child: Padding(
                padding: EdgeInsets.all(
                  isTablet ? AppTheme.spacing20 : AppTheme.spacing16,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header with status
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: AppTheme.spacing12,
                            vertical: AppTheme.spacing4,
                          ),
                          decoration: BoxDecoration(
                            color:
                                _isLessonCompleted(lesson.id)
                                    ? AppTheme.successColor.withValues(
                                      alpha: 0.1,
                                    )
                                    : AppTheme.primaryColor.withValues(
                                      alpha: 0.1,
                                    ),
                            borderRadius: BorderRadius.circular(
                              AppTheme.radiusSmall,
                            ),
                          ),
                          child: Text(
                            _isLessonCompleted(lesson.id)
                                ? 'Byarangiye'
                                : _formatDifficulty(lesson.difficulty),
                            style: AppTheme.bodySmall.copyWith(
                              color:
                                  _isLessonCompleted(lesson.id)
                                      ? AppTheme.successColor
                                      : AppTheme.primaryColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        Icon(
                          _isLessonCompleted(lesson.id)
                              ? Icons.check_circle_rounded
                              : Icons.play_circle_rounded,
                          color:
                              _isLessonCompleted(lesson.id)
                                  ? AppTheme.successColor
                                  : AppTheme.primaryColor,
                          size: isTablet ? 28 : 24,
                        ),
                      ],
                    ),

                    SizedBox(height: AppTheme.spacing16),

                    // Title
                    Text(
                      lesson.title,
                      style: AppTheme.headingSmall.copyWith(
                        fontSize: isTablet ? 18 : 16,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    SizedBox(height: AppTheme.spacing8),

                    // Description
                    Text(
                      lesson.content,
                      style: AppTheme.bodySmall.copyWith(
                        color: AppTheme.textSecondary,
                        height: 1.4,
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const Spacer(),

                    // Progress bar
                    if (_getLessonProgressPercentage(lesson.id) > 0) ...[
                      SizedBox(height: AppTheme.spacing12),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Uko ugeze',
                                style: AppTheme.bodySmall.copyWith(
                                  color: AppTheme.textTertiary,
                                ),
                              ),
                              Text(
                                '${_getLessonProgressPercentage(lesson.id)}%',
                                style: AppTheme.bodySmall.copyWith(
                                  color: AppTheme.primaryColor,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: AppTheme.spacing4),
                          LinearProgressIndicator(
                            value:
                                _getLessonProgressPercentage(lesson.id) / 100,
                            backgroundColor: AppTheme.primaryColor.withValues(
                              alpha: 0.1,
                            ),
                            valueColor: AlwaysStoppedAnimation<Color>(
                              _isLessonCompleted(lesson.id)
                                  ? AppTheme.successColor
                                  : AppTheme.primaryColor,
                            ),
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ],
                      ),
                    ],

                    SizedBox(height: AppTheme.spacing12),

                    // Duration
                    Row(
                      children: [
                        Icon(
                          Icons.access_time_rounded,
                          size: 16,
                          color: AppTheme.textTertiary,
                        ),
                        SizedBox(width: AppTheme.spacing4),
                        Text(
                          _formatDuration(lesson.estimatedDuration),
                          style: AppTheme.bodySmall.copyWith(
                            color: AppTheme.textTertiary,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        )
        .animate(delay: (index * 150).ms)
        .fadeIn()
        .slideY(begin: 0.3, duration: 600.ms);
  }
}

class EducationCategory {
  final String id;
  final String name;
  final IconData icon;

  EducationCategory({required this.id, required this.name, required this.icon});
}
