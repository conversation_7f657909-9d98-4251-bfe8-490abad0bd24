import 'dart:convert';
import 'package:http/http.dart' as http;
import '../constants/app_constants.dart';
import 'auth_service.dart';

enum TicketType { technical, medical, account, feedback, complaint, suggestion }
enum TicketPriority { low, medium, high, urgent }
enum TicketStatus { open, inProgress, resolved, closed }

class SupportTicket {
  final String id;
  final String? userId;
  final TicketType ticketType;
  final String subject;
  final String description;
  final TicketPriority priority;
  final TicketStatus status;
  final String? assignedTo;
  final String? resolutionNotes;
  final String? userEmail;
  final String? userPhone;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? resolvedAt;

  SupportTicket({
    required this.id,
    this.userId,
    required this.ticketType,
    required this.subject,
    required this.description,
    required this.priority,
    required this.status,
    this.assignedTo,
    this.resolutionNotes,
    this.userEmail,
    this.userPhone,
    required this.createdAt,
    required this.updatedAt,
    this.resolvedAt,
  });

  factory SupportTicket.fromJson(Map<String, dynamic> json) {
    return SupportTicket(
      id: json['id'].toString(),
      userId: json['user']?['id']?.toString(),
      ticketType: TicketType.values.firstWhere(
        (e) => e.name.toUpperCase() == json['ticketType'].toString().toUpperCase(),
      ),
      subject: json['subject'],
      description: json['description'],
      priority: TicketPriority.values.firstWhere(
        (e) => e.name.toUpperCase() == json['priority'].toString().toUpperCase(),
      ),
      status: TicketStatus.values.firstWhere(
        (e) => e.name.toUpperCase() == json['status'].toString().toUpperCase(),
      ),
      assignedTo: json['assignedTo']?['id']?.toString(),
      resolutionNotes: json['resolutionNotes'],
      userEmail: json['userEmail'],
      userPhone: json['userPhone'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      resolvedAt: json['resolvedAt'] != null ? DateTime.parse(json['resolvedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'ticketType': ticketType.name.toUpperCase(),
      'subject': subject,
      'description': description,
      'priority': priority.name.toUpperCase(),
      'status': status.name.toUpperCase(),
      'userEmail': userEmail,
      'userPhone': userPhone,
    };
  }
}

class SupportTicketService {
  final String baseUrl = AppConstants.baseUrl;
  final AuthService _authService = AuthService();

  Future<String?> _getAuthToken() async {
    return _authService.currentToken;
  }

  Future<String?> _getCurrentUserId() async {
    final user = await _authService.getCurrentUser();
    return user?.id;
  }

  Map<String, String> _getHeaders(String? token) {
    final headers = {
      'Content-Type': 'application/json',
    };
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }

  /// Get support tickets
  Future<List<SupportTicket>> getSupportTickets({
    TicketStatus? status,
    TicketType? type,
    TicketPriority? priority,
  }) async {
    try {
      final token = await _getAuthToken();
      final userId = await _getCurrentUserId();

      final queryParams = <String, String>{};
      if (userId != null) queryParams['userId'] = userId;
      if (status != null) queryParams['status'] = status.name.toUpperCase();
      if (type != null) queryParams['type'] = type.name.toUpperCase();
      if (priority != null) queryParams['priority'] = priority.name.toUpperCase();

      final uri = Uri.parse('$baseUrl/support-tickets')
          .replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final List<dynamic> ticketsJson = data['tickets'];
          return ticketsJson.map((json) => SupportTicket.fromJson(json)).toList();
        }
      }

      return [];
    } catch (e) {
      print('Error loading support tickets: $e');
      return [];
    }
  }

  /// Create support ticket
  Future<SupportTicket?> createSupportTicket({
    required TicketType ticketType,
    required String subject,
    required String description,
    TicketPriority priority = TicketPriority.medium,
    String? userEmail,
    String? userPhone,
  }) async {
    try {
      final token = await _getAuthToken();
      final userId = await _getCurrentUserId();

      final requestData = {
        'ticketType': ticketType.name.toUpperCase(),
        'subject': subject,
        'description': description,
        'priority': priority.name.toUpperCase(),
        'userEmail': userEmail,
        'userPhone': userPhone,
      };

      if (userId != null) {
        requestData['userId'] = userId;
      }

      final uri = Uri.parse('$baseUrl/support-tickets');
      final body = json.encode(requestData);

      final response = await http.post(
        uri,
        headers: _getHeaders(token),
        body: body,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return SupportTicket.fromJson(data['ticket']);
        }
      }

      return null;
    } catch (e) {
      print('Error creating support ticket: $e');
      return null;
    }
  }

  /// Update support ticket
  Future<SupportTicket?> updateSupportTicket({
    required String ticketId,
    TicketStatus? status,
    TicketPriority? priority,
    String? resolutionNotes,
  }) async {
    try {
      final token = await _getAuthToken();

      final requestData = <String, dynamic>{};
      if (status != null) requestData['status'] = status.name.toUpperCase();
      if (priority != null) requestData['priority'] = priority.name.toUpperCase();
      if (resolutionNotes != null) requestData['resolutionNotes'] = resolutionNotes;

      final uri = Uri.parse('$baseUrl/support-tickets/$ticketId');
      final body = json.encode(requestData);

      final response = await http.put(
        uri,
        headers: _getHeaders(token),
        body: body,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return SupportTicket.fromJson(data['ticket']);
        }
      }

      return null;
    } catch (e) {
      print('Error updating support ticket: $e');
      return null;
    }
  }

  /// Resolve support ticket
  Future<SupportTicket?> resolveSupportTicket({
    required String ticketId,
    required String resolutionNotes,
  }) async {
    try {
      final token = await _getAuthToken();

      final requestData = {
        'resolutionNotes': resolutionNotes,
      };

      final uri = Uri.parse('$baseUrl/support-tickets/$ticketId/resolve');
      final body = json.encode(requestData);

      final response = await http.post(
        uri,
        headers: _getHeaders(token),
        body: body,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return SupportTicket.fromJson(data['ticket']);
        }
      }

      return null;
    } catch (e) {
      print('Error resolving support ticket: $e');
      return null;
    }
  }

  /// Delete support ticket
  Future<bool> deleteSupportTicket(String ticketId) async {
    try {
      final token = await _getAuthToken();
      final userId = await _getCurrentUserId();
      
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final uri = Uri.parse('$baseUrl/support-tickets/$ticketId?userId=$userId');

      final response = await http.delete(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['success'] == true;
      }

      return false;
    } catch (e) {
      print('Error deleting support ticket: $e');
      return false;
    }
  }

  /// Get ticket statistics
  Future<Map<String, int>?> getTicketStats() async {
    try {
      final token = await _getAuthToken();

      final uri = Uri.parse('$baseUrl/support-tickets/stats');
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final stats = data['stats'];
          return {
            'open': stats['open'],
            'inProgress': stats['inProgress'],
            'resolved': stats['resolved'],
            'closed': stats['closed'],
            'total': stats['total'],
          };
        }
      }

      return null;
    } catch (e) {
      print('Error loading ticket stats: $e');
      return null;
    }
  }
}
