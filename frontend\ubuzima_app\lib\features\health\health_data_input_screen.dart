import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/theme/app_theme.dart';
import '../../core/services/health_tracking_service.dart';
import '../../widgets/voice_button.dart';

class HealthDataInputScreen extends StatefulWidget {
  final String? metricType; // 'heart_rate', 'weight', 'blood_pressure', 'temperature'
  
  const HealthDataInputScreen({super.key, this.metricType});

  @override
  State<HealthDataInputScreen> createState() => _HealthDataInputScreenState();
}

class _HealthDataInputScreenState extends State<HealthDataInputScreen> {
  final HealthTrackingService _healthService = HealthTrackingService();
  final _formKey = GlobalKey<FormState>();
  
  // Controllers for different metrics
  final _heartRateController = TextEditingController();
  final _weightController = TextEditingController();
  final _systolicController = TextEditingController();
  final _diastolicController = TextEditingController();
  final _temperatureController = TextEditingController();
  final _notesController = TextEditingController();
  
  String _selectedMetric = 'heart_rate';
  bool _isLoading = false;
  DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    if (widget.metricType != null) {
      _selectedMetric = widget.metricType!;
    }
  }

  @override
  void dispose() {
    _heartRateController.dispose();
    _weightController.dispose();
    _systolicController.dispose();
    _diastolicController.dispose();
    _temperatureController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _handleVoiceInput(String command) {
    final lowerCommand = command.toLowerCase();
    
    // Parse voice commands like "heart rate 75" or "weight 65 kg"
    if (lowerCommand.contains('heart') || lowerCommand.contains('umutima')) {
      _selectedMetric = 'heart_rate';
      final match = RegExp(r'(\d+)').firstMatch(lowerCommand);
      if (match != null) {
        _heartRateController.text = match.group(1)!;
      }
    } else if (lowerCommand.contains('weight') || lowerCommand.contains('ibiro')) {
      _selectedMetric = 'weight';
      final match = RegExp(r'(\d+(?:\.\d+)?)').firstMatch(lowerCommand);
      if (match != null) {
        _weightController.text = match.group(1)!;
      }
    } else if (lowerCommand.contains('pressure') || lowerCommand.contains('amaraso')) {
      _selectedMetric = 'blood_pressure';
      final match = RegExp(r'(\d+)[/\s]+(\d+)').firstMatch(lowerCommand);
      if (match != null) {
        _systolicController.text = match.group(1)!;
        _diastolicController.text = match.group(2)!;
      }
    } else if (lowerCommand.contains('temperature') || lowerCommand.contains('bushyuhe')) {
      _selectedMetric = 'temperature';
      final match = RegExp(r'(\d+(?:\.\d+)?)').firstMatch(lowerCommand);
      if (match != null) {
        _temperatureController.text = match.group(1)!;
      }
    }
    
    setState(() {});
  }

  Future<void> _saveHealthData() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      Map<String, dynamic> data = {
        'recordedAt': _selectedDate.toIso8601String(),
        'notes': _notesController.text,
      };

      switch (_selectedMetric) {
        case 'heart_rate':
          data['value'] = int.parse(_heartRateController.text);
          data['unit'] = 'bpm';
          break;
        case 'weight':
          data['value'] = double.parse(_weightController.text);
          data['unit'] = 'kg';
          break;
        case 'blood_pressure':
          data['systolic'] = int.parse(_systolicController.text);
          data['diastolic'] = int.parse(_diastolicController.text);
          data['unit'] = 'mmHg';
          break;
        case 'temperature':
          data['value'] = double.parse(_temperatureController.text);
          data['unit'] = '°C';
          break;
      }

      await _healthService.addHealthRecord(_selectedMetric, data);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Amakuru y\'ubuzima yabitswe neza'),
            backgroundColor: AppTheme.successColor,
          ),
        );
        Navigator.pop(context, true); // Return true to indicate data was saved
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Habaye ikosa mu kubika amakuru'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width > 600;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          'Ongeraho amakuru y\'ubuzima',
          style: AppTheme.headingMedium.copyWith(
            color: Colors.white,
            fontSize: isTablet ? 24 : 20,
          ),
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(isTablet ? AppTheme.spacing24 : AppTheme.spacing16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Metric Type Selector
              _buildMetricSelector(isTablet),
              SizedBox(height: AppTheme.spacing24),
              
              // Date Selector
              _buildDateSelector(isTablet),
              SizedBox(height: AppTheme.spacing24),
              
              // Input Fields based on selected metric
              _buildInputFields(isTablet),
              SizedBox(height: AppTheme.spacing24),
              
              // Notes Field
              _buildNotesField(isTablet),
              SizedBox(height: AppTheme.spacing32),
              
              // Save Button
              SizedBox(
                width: double.infinity,
                height: isTablet ? 56 : 48,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _saveHealthData,
                  style: AppTheme.primaryButtonStyle,
                  child: _isLoading
                      ? CircularProgressIndicator(color: Colors.white)
                      : Text(
                          'Bika amakuru',
                          style: AppTheme.labelLarge.copyWith(color: Colors.white),
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: VoiceButton(
        prompt: 'Vuga amakuru y\'ubuzima - urugero: "Umutima 75" cyangwa "Ibiro 65"',
        onResult: _handleVoiceInput,
        tooltip: 'Koresha ijwi',
      ),
    );
  }

  Widget _buildMetricSelector(bool isTablet) {
    final metrics = [
      {'key': 'heart_rate', 'label': 'Umuvuduko w\'umutima', 'icon': Icons.favorite_rounded, 'color': AppTheme.errorColor},
      {'key': 'weight', 'label': 'Ibiro', 'icon': Icons.monitor_weight_rounded, 'color': AppTheme.primaryColor},
      {'key': 'blood_pressure', 'label': 'Umuvuduko w\'amaraso', 'icon': Icons.bloodtype_rounded, 'color': AppTheme.successColor},
      {'key': 'temperature', 'label': 'Ubushyuhe bw\'umubiri', 'icon': Icons.thermostat_rounded, 'color': AppTheme.secondaryColor},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Hitamo ubwoko bw\'amakuru',
          style: AppTheme.headingSmall,
        ),
        SizedBox(height: AppTheme.spacing16),
        GridView.builder(
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: isTablet ? 4 : 2,
            crossAxisSpacing: AppTheme.spacing12,
            mainAxisSpacing: AppTheme.spacing12,
            childAspectRatio: isTablet ? 1.2 : 1.5,
          ),
          itemCount: metrics.length,
          itemBuilder: (context, index) {
            final metric = metrics[index];
            final isSelected = _selectedMetric == metric['key'];
            
            return GestureDetector(
              onTap: () => setState(() => _selectedMetric = metric['key'] as String),
              child: Container(
                decoration: BoxDecoration(
                  color: isSelected ? (metric['color'] as Color).withValues(alpha: 0.1) : AppTheme.surfaceColor,
                  borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
                  border: Border.all(
                    color: isSelected ? (metric['color'] as Color) : AppTheme.borderColor,
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      metric['icon'] as IconData,
                      color: isSelected ? (metric['color'] as Color) : AppTheme.textSecondary,
                      size: isTablet ? 32 : 24,
                    ),
                    SizedBox(height: AppTheme.spacing8),
                    Text(
                      metric['label'] as String,
                      style: AppTheme.bodySmall.copyWith(
                        color: isSelected ? (metric['color'] as Color) : AppTheme.textSecondary,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ).animate(delay: (index * 100).ms).fadeIn().scale();
          },
        ),
      ],
    );
  }

  Widget _buildDateSelector(bool isTablet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Itariki',
          style: AppTheme.headingSmall,
        ),
        SizedBox(height: AppTheme.spacing12),
        GestureDetector(
          onTap: () async {
            final date = await showDatePicker(
              context: context,
              initialDate: _selectedDate,
              firstDate: DateTime.now().subtract(Duration(days: 365)),
              lastDate: DateTime.now(),
            );
            if (date != null) {
              setState(() => _selectedDate = date);
            }
          },
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.all(AppTheme.spacing16),
            decoration: BoxDecoration(
              color: AppTheme.surfaceColor,
              borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
              border: Border.all(color: AppTheme.borderColor),
            ),
            child: Row(
              children: [
                Icon(Icons.calendar_today_rounded, color: AppTheme.primaryColor),
                SizedBox(width: AppTheme.spacing12),
                Text(
                  '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                  style: AppTheme.bodyLarge,
                ),
                Spacer(),
                Icon(Icons.arrow_drop_down_rounded, color: AppTheme.textSecondary),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInputFields(bool isTablet) {
    switch (_selectedMetric) {
      case 'heart_rate':
        return _buildHeartRateInput(isTablet);
      case 'weight':
        return _buildWeightInput(isTablet);
      case 'blood_pressure':
        return _buildBloodPressureInput(isTablet);
      case 'temperature':
        return _buildTemperatureInput(isTablet);
      default:
        return SizedBox.shrink();
    }
  }

  Widget _buildHeartRateInput(bool isTablet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Umuvuduko w\'umutima', style: AppTheme.headingSmall),
        SizedBox(height: AppTheme.spacing12),
        TextFormField(
          controller: _heartRateController,
          keyboardType: TextInputType.number,
          decoration: InputDecoration(
            labelText: 'Umuvuduko (bpm)',
            hintText: 'Urugero: 75',
            suffixText: 'bpm',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(AppTheme.radiusLarge)),
          ),
          validator: (value) {
            if (value?.isEmpty ?? true) return 'Injiza umuvuduko w\'umutima';
            final rate = int.tryParse(value!);
            if (rate == null || rate < 30 || rate > 200) {
              return 'Injiza umuvuduko uri hagati ya 30 na 200';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildWeightInput(bool isTablet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Ibiro', style: AppTheme.headingSmall),
        SizedBox(height: AppTheme.spacing12),
        TextFormField(
          controller: _weightController,
          keyboardType: TextInputType.numberWithOptions(decimal: true),
          decoration: InputDecoration(
            labelText: 'Ibiro (kg)',
            hintText: 'Urugero: 65.5',
            suffixText: 'kg',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(AppTheme.radiusLarge)),
          ),
          validator: (value) {
            if (value?.isEmpty ?? true) return 'Injiza ibiro byawe';
            final weight = double.tryParse(value!);
            if (weight == null || weight < 20 || weight > 300) {
              return 'Injiza ibiro biri hagati ya 20 na 300';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildBloodPressureInput(bool isTablet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Umuvuduko w\'amaraso', style: AppTheme.headingSmall),
        SizedBox(height: AppTheme.spacing12),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _systolicController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'Systolic',
                  hintText: '120',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(AppTheme.radiusLarge)),
                ),
                validator: (value) {
                  if (value?.isEmpty ?? true) return 'Injiza systolic';
                  final sys = int.tryParse(value!);
                  if (sys == null || sys < 70 || sys > 250) {
                    return '70-250';
                  }
                  return null;
                },
              ),
            ),
            SizedBox(width: AppTheme.spacing16),
            Text('/', style: AppTheme.headingLarge),
            SizedBox(width: AppTheme.spacing16),
            Expanded(
              child: TextFormField(
                controller: _diastolicController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'Diastolic',
                  hintText: '80',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(AppTheme.radiusLarge)),
                ),
                validator: (value) {
                  if (value?.isEmpty ?? true) return 'Injiza diastolic';
                  final dia = int.tryParse(value!);
                  if (dia == null || dia < 40 || dia > 150) {
                    return '40-150';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        SizedBox(height: AppTheme.spacing8),
        Text(
          'mmHg',
          style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
        ),
      ],
    );
  }

  Widget _buildTemperatureInput(bool isTablet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Ubushyuhe bw\'umubiri', style: AppTheme.headingSmall),
        SizedBox(height: AppTheme.spacing12),
        TextFormField(
          controller: _temperatureController,
          keyboardType: TextInputType.numberWithOptions(decimal: true),
          decoration: InputDecoration(
            labelText: 'Ubushyuhe (°C)',
            hintText: 'Urugero: 36.5',
            suffixText: '°C',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(AppTheme.radiusLarge)),
          ),
          validator: (value) {
            if (value?.isEmpty ?? true) return 'Injiza ubushyuhe bw\'umubiri';
            final temp = double.tryParse(value!);
            if (temp == null || temp < 30 || temp > 45) {
              return 'Injiza ubushyuhe buri hagati ya 30 na 45';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildNotesField(bool isTablet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Inyongera (optional)', style: AppTheme.headingSmall),
        SizedBox(height: AppTheme.spacing12),
        TextFormField(
          controller: _notesController,
          maxLines: 3,
          decoration: InputDecoration(
            labelText: 'Andika inyongera',
            hintText: 'Urugero: Nakunze gukora siporo...',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(AppTheme.radiusLarge)),
          ),
        ),
      ],
    );
  }
}
