import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../constants/app_constants.dart';
import '../models/contraception_model.dart';
import 'auth_service.dart';

class ContraceptionService {
  static final ContraceptionService _instance =
      ContraceptionService._internal();
  factory ContraceptionService() => _instance;
  ContraceptionService._internal();

  final String baseUrl = AppConstants.baseUrl;
  final AuthService _authService = AuthService();

  Future<String?> _getAuthToken() async {
    return _authService.currentToken;
  }

  Map<String, String> _getHeaders([String? token]) {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }

  Future<List<ContraceptionMethod>> getContraceptionMethods({
    String? userId,
  }) async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse(
        '$baseUrl/contraception${userId != null ? '?userId=$userId' : ''}',
      );

      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final List<dynamic> methods = data['contraceptionMethods'];
          return methods
              .map((json) => ContraceptionMethod.fromJson(json))
              .toList();
        }
      }

      throw Exception('Failed to load contraception methods');
    } catch (e) {
      debugPrint('Error loading contraception methods: $e');
      return [];
    }
  }

  Future<ContraceptionMethod?> createContraceptionMethod(
    Map<String, dynamic> methodData,
  ) async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/contraception');

      final response = await http.post(
        uri,
        headers: _getHeaders(token),
        body: json.encode(methodData),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return ContraceptionMethod.fromJson(data['contraceptionMethod']);
        }
      }

      throw Exception('Failed to create contraception method');
    } catch (e) {
      debugPrint('Error creating contraception method: $e');
      return null;
    }
  }

  Future<ContraceptionMethod?> updateContraceptionMethod(
    String id,
    Map<String, dynamic> methodData,
  ) async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/contraception/$id');

      final response = await http.put(
        uri,
        headers: _getHeaders(token),
        body: json.encode(methodData),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return ContraceptionMethod.fromJson(data['contraceptionMethod']);
        }
      }

      throw Exception('Failed to update contraception method');
    } catch (e) {
      debugPrint('Error updating contraception method: $e');
      return null;
    }
  }

  Future<bool> deleteContraceptionMethod(String id) async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/contraception/$id');

      final response = await http.delete(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['success'] == true;
      }

      return false;
    } catch (e) {
      debugPrint('Error deleting contraception method: $e');
      return false;
    }
  }

  Future<ContraceptionMethod?> getActiveContraception({String? userId}) async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse(
        '$baseUrl/contraception/active${userId != null ? '?userId=$userId' : ''}',
      );

      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['activeContraception'] != null) {
          return ContraceptionMethod.fromJson(data['activeContraception']);
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error loading active contraception: $e');
      return null;
    }
  }

  Future<List<ContraceptionType>> getContraceptionTypes() async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/contraception/types');

      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final List<dynamic> types = data['contraceptionTypes'];
          return types
              .map(
                (type) => ContraceptionType.values.firstWhere(
                  (e) =>
                      e.toString().split('.').last ==
                      type.toString().toLowerCase(),
                  orElse: () => ContraceptionType.pill,
                ),
              )
              .toList();
        }
      }

      return ContraceptionType.values;
    } catch (e) {
      debugPrint('Error loading contraception types: $e');
      return ContraceptionType.values;
    }
  }
}
