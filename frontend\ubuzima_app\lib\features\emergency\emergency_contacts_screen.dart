import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../core/theme/app_theme.dart';
import '../../core/services/emergency_service.dart';
import '../../widgets/voice_button.dart';

class EmergencyContactsScreen extends StatefulWidget {
  const EmergencyContactsScreen({super.key});

  @override
  State<EmergencyContactsScreen> createState() => _EmergencyContactsScreenState();
}

class _EmergencyContactsScreenState extends State<EmergencyContactsScreen> {
  final EmergencyService _emergencyService = EmergencyService();
  List<EmergencyContact> _emergencyContacts = [];
  List<EmergencyContact> _systemContacts = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadEmergencyContacts();
  }

  Future<void> _loadEmergencyContacts() async {
    setState(() => _isLoading = true);
    
    try {
      final contacts = await _emergencyService.getEmergencyContacts();
      final systemContacts = await _emergencyService.getSystemEmergencyContacts();
      
      setState(() {
        _emergencyContacts = contacts;
        _systemContacts = systemContacts;
      });
    } catch (e) {
      _showErrorSnackBar('Habaye ikosa mu gushaka ababazo b\'ihutirwa');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.errorColor,
      ),
    );
  }

  void _handleVoiceCommand(String command) {
    final lowerCommand = command.toLowerCase();
    if (lowerCommand.contains('hamagara') || lowerCommand.contains('call')) {
      // Extract contact name or number from command
      _handleEmergencyCall();
    } else if (lowerCommand.contains('kongeraho') || lowerCommand.contains('add')) {
      _showAddContactDialog();
    }
  }

  void _handleEmergencyCall() {
    if (_systemContacts.isNotEmpty) {
      _makeCall(_systemContacts.first.phoneNumber);
    }
  }

  Future<void> _makeCall(String phoneNumber) async {
    final uri = Uri(scheme: 'tel', path: phoneNumber);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      _showErrorSnackBar('Ntibishoboka guhamagara uyu numero');
    }
  }

  void _showAddContactDialog() {
    final nameController = TextEditingController();
    final phoneController = TextEditingController();
    final relationshipController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Ongeraho umunyamuryango w\'ihutirwa'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: InputDecoration(
                labelText: 'Izina',
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: AppTheme.spacing16),
            TextField(
              controller: phoneController,
              decoration: InputDecoration(
                labelText: 'Telefoni',
                border: OutlineInputBorder(),
                hintText: '+250XXXXXXXXX',
              ),
              keyboardType: TextInputType.phone,
            ),
            SizedBox(height: AppTheme.spacing16),
            TextField(
              controller: relationshipController,
              decoration: InputDecoration(
                labelText: 'Isano',
                border: OutlineInputBorder(),
                hintText: 'Urugero: Umubyeyi, Umugabo, Inshuti',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Hagarika'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (nameController.text.isNotEmpty && phoneController.text.isNotEmpty) {
                try {
                  await _emergencyService.addEmergencyContact(
                    name: nameController.text,
                    phoneNumber: phoneController.text,
                    relationship: relationshipController.text,
                  );
                  Navigator.pop(context);
                  await _loadEmergencyContacts();
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Umunyamuryango w\'ihutirwa yongerewe'),
                        backgroundColor: AppTheme.successColor,
                      ),
                    );
                  }
                } catch (e) {
                  _showErrorSnackBar('Habaye ikosa mu kongeraho umunyamuryango');
                }
              }
            },
            child: Text('Bika'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width > 600;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          'Ababazo b\'ihutirwa',
          style: AppTheme.headingMedium.copyWith(
            color: Colors.white,
            fontSize: isTablet ? 24 : 20,
          ),
        ),
        backgroundColor: AppTheme.errorColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(Icons.add_rounded),
            onPressed: _showAddContactDialog,
            tooltip: 'Ongeraho umunyamuryango',
          ),
        ],
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: EdgeInsets.all(isTablet ? AppTheme.spacing24 : AppTheme.spacing16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Emergency Call Button
                  Container(
                    width: double.infinity,
                    height: isTablet ? 120 : 100,
                    margin: EdgeInsets.only(bottom: AppTheme.spacing24),
                    child: ElevatedButton(
                      onPressed: _handleEmergencyCall,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.errorColor,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
                        ),
                        elevation: 8,
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.emergency_rounded,
                            size: isTablet ? 48 : 40,
                          ),
                          SizedBox(height: AppTheme.spacing8),
                          Text(
                            'HAMAGARA IHUTIRWA',
                            style: AppTheme.headingSmall.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ).animate().scale(delay: 100.ms).then().shake(),

                  // System Emergency Contacts
                  if (_systemContacts.isNotEmpty) ...[
                    Text(
                      'Ababazo ba sisitemu',
                      style: AppTheme.headingSmall,
                    ),
                    SizedBox(height: AppTheme.spacing16),
                    ..._systemContacts.asMap().entries.map((entry) {
                      final index = entry.key;
                      final contact = entry.value;
                      return _buildContactCard(contact, isTablet, index, isSystem: true);
                    }),
                    SizedBox(height: AppTheme.spacing24),
                  ],

                  // Personal Emergency Contacts
                  Text(
                    'Ababazo bawe',
                    style: AppTheme.headingSmall,
                  ),
                  SizedBox(height: AppTheme.spacing16),
                  
                  if (_emergencyContacts.isEmpty)
                    _buildEmptyState(isTablet)
                  else
                    ..._emergencyContacts.asMap().entries.map((entry) {
                      final index = entry.key;
                      final contact = entry.value;
                      return _buildContactCard(contact, isTablet, index);
                    }),
                ],
              ),
            ),
      floatingActionButton: VoiceButton(
        prompt: 'Vuga icyo ushaka gukora - urugero: "Hamagara ihutirwa" cyangwa "Ongeraho umunyamuryango"',
        onResult: _handleVoiceCommand,
        tooltip: 'Koresha ijwi',
      ),
    );
  }

  Widget _buildEmptyState(bool isTablet) {
    return Center(
      child: Column(
        children: [
          SizedBox(height: AppTheme.spacing32),
          Icon(
            Icons.contact_emergency_rounded,
            size: isTablet ? 80 : 64,
            color: AppTheme.textTertiary,
          ),
          SizedBox(height: AppTheme.spacing16),
          Text(
            'Nta babazo b\'ihutirwa',
            style: AppTheme.headingSmall.copyWith(
              color: AppTheme.textTertiary,
            ),
          ),
          SizedBox(height: AppTheme.spacing8),
          Text(
            'Ongeraho ababazo b\'ihutirwa kugira ngo ushobore kubafata vuba',
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: AppTheme.spacing24),
          ElevatedButton.icon(
            onPressed: _showAddContactDialog,
            icon: Icon(Icons.add_rounded),
            label: Text('Ongeraho umunyamuryango'),
            style: AppTheme.primaryButtonStyle,
          ),
        ],
      ),
    );
  }

  Widget _buildContactCard(EmergencyContact contact, bool isTablet, int index, {bool isSystem = false}) {
    return Container(
      margin: EdgeInsets.only(bottom: AppTheme.spacing12),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
        boxShadow: AppTheme.softShadow,
        border: isSystem ? Border.all(color: AppTheme.errorColor.withValues(alpha: 0.3)) : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _makeCall(contact.phoneNumber),
          borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
          child: Padding(
            padding: EdgeInsets.all(isTablet ? AppTheme.spacing20 : AppTheme.spacing16),
            child: Row(
              children: [
                Container(
                  width: isTablet ? 56 : 48,
                  height: isTablet ? 56 : 48,
                  decoration: BoxDecoration(
                    color: isSystem ? AppTheme.errorColor.withValues(alpha: 0.1) : AppTheme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(isTablet ? 28 : 24),
                  ),
                  child: Icon(
                    isSystem ? Icons.local_hospital_rounded : Icons.person_rounded,
                    color: isSystem ? AppTheme.errorColor : AppTheme.primaryColor,
                    size: isTablet ? 28 : 24,
                  ),
                ),
                SizedBox(width: AppTheme.spacing16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        contact.name,
                        style: AppTheme.labelLarge.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      SizedBox(height: AppTheme.spacing4),
                      Text(
                        contact.phoneNumber,
                        style: AppTheme.bodyMedium.copyWith(
                          color: AppTheme.textSecondary,
                        ),
                      ),
                      if (contact.relationship.isNotEmpty) ...[
                        SizedBox(height: AppTheme.spacing2),
                        Text(
                          contact.relationship,
                          style: AppTheme.bodySmall.copyWith(
                            color: AppTheme.textTertiary,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Icon(
                  Icons.phone_rounded,
                  color: AppTheme.successColor,
                  size: isTablet ? 24 : 20,
                ),
                if (!isSystem) ...[
                  SizedBox(width: AppTheme.spacing8),
                  IconButton(
                    icon: Icon(Icons.delete_rounded),
                    color: AppTheme.errorColor,
                    onPressed: () => _deleteContact(contact),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    ).animate(delay: (index * 100).ms).fadeIn().slideX(begin: 0.3);
  }

  void _deleteContact(EmergencyContact contact) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Gusiba umunyamuryango'),
        content: Text('Urashaka gusiba ${contact.name} mu babazo b\'ihutirwa?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('Hagarika'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: AppTheme.errorColor),
            child: Text('Siba'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _emergencyService.deleteEmergencyContact(contact.id);
        await _loadEmergencyContacts();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Umunyamuryango yasibwe'),
              backgroundColor: AppTheme.successColor,
            ),
          );
        }
      } catch (e) {
        _showErrorSnackBar('Habaye ikosa mu gusiba umunyamuryango');
      }
    }
  }
}
