package rw.health.ubuzima.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import rw.health.ubuzima.entity.EducationLesson;
import rw.health.ubuzima.enums.EducationCategory;
import rw.health.ubuzima.enums.EducationLevel;

import java.util.List;

@Repository
public interface EducationLessonRepository extends JpaRepository<EducationLesson, Long> {
    
    List<EducationLesson> findByIsPublishedTrueOrderByOrderIndexAsc();
    
    List<EducationLesson> findByCategoryAndIsPublishedTrueOrderByOrderIndexAsc(EducationCategory category);
    
    List<EducationLesson> findByLevelAndIsPublishedTrueOrderByOrderIndexAsc(EducationLevel level);
    
    List<EducationLesson> findByCategoryAndLevelAndIsPublishedTrueOrderByOrderIndexAsc(EducationCategory category, EducationLevel level);
    
    List<EducationLesson> findByLanguageAndIsPublishedTrueOrderByOrderIndexAsc(String language);
    
    @Query("SELECT l FROM EducationLesson l WHERE l.isPublished = true AND (l.title LIKE %:searchTerm% OR l.description LIKE %:searchTerm%) ORDER BY l.orderIndex ASC")
    List<EducationLesson> searchLessons(@Param("searchTerm") String searchTerm);
    
    @Query("SELECT l FROM EducationLesson l WHERE l.isPublished = true ORDER BY l.viewCount DESC LIMIT 10")
    List<EducationLesson> findMostPopularLessons();

    List<EducationLesson> findByAuthorAndIsPublishedTrueOrderByCreatedAtDesc(String author);

    @Query("SELECT l FROM EducationLesson l WHERE l.isPublished = true AND l.id NOT IN :completedLessonIds ORDER BY l.viewCount DESC, l.createdAt DESC LIMIT 10")
    List<EducationLesson> findRecommendedLessons(@Param("completedLessonIds") List<Long> completedLessonIds);

    Long countByIsPublishedTrue();
}
