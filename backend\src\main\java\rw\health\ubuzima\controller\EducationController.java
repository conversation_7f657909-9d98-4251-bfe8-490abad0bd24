package rw.health.ubuzima.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import rw.health.ubuzima.entity.EducationLesson;
import rw.health.ubuzima.entity.EducationProgress;
import rw.health.ubuzima.entity.User;
import rw.health.ubuzima.enums.EducationCategory;
import rw.health.ubuzima.enums.EducationLevel;
import rw.health.ubuzima.repository.EducationLessonRepository;
import rw.health.ubuzima.repository.EducationProgressRepository;
import rw.health.ubuzima.repository.UserRepository;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/education")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class EducationController {

    private final EducationLessonRepository educationLessonRepository;
    private final EducationProgressRepository educationProgressRepository;
    private final UserRepository userRepository;

    @GetMapping("/lessons")
    public ResponseEntity<Map<String, Object>> getEducationLessons(
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String level,
            @RequestParam(required = false) String language) {
        
        try {
            List<EducationLesson> lessons;

            if (category != null && level != null) {
                EducationCategory cat = EducationCategory.valueOf(category.toUpperCase());
                EducationLevel lvl = EducationLevel.valueOf(level.toUpperCase());
                lessons = educationLessonRepository.findByCategoryAndLevelAndIsPublishedTrueOrderByOrderIndexAsc(cat, lvl);
            } else if (category != null) {
                EducationCategory cat = EducationCategory.valueOf(category.toUpperCase());
                lessons = educationLessonRepository.findByCategoryAndIsPublishedTrueOrderByOrderIndexAsc(cat);
            } else if (level != null) {
                EducationLevel lvl = EducationLevel.valueOf(level.toUpperCase());
                lessons = educationLessonRepository.findByLevelAndIsPublishedTrueOrderByOrderIndexAsc(lvl);
            } else if (language != null) {
                lessons = educationLessonRepository.findByLanguageAndIsPublishedTrueOrderByOrderIndexAsc(language);
            } else {
                lessons = educationLessonRepository.findByIsPublishedTrueOrderByOrderIndexAsc();
            }

            return ResponseEntity.ok(Map.of(
                "success", true,
                "lessons", lessons
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch education lessons: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/lessons/{id}")
    public ResponseEntity<Map<String, Object>> getEducationLesson(@PathVariable Long id) {
        try {
            EducationLesson lesson = educationLessonRepository.findById(id).orElse(null);
            
            if (lesson == null) {
                return ResponseEntity.notFound().build();
            }

            // Increment view count
            lesson.setViewCount(lesson.getViewCount() + 1);
            educationLessonRepository.save(lesson);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "lesson", lesson
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch education lesson: " + e.getMessage()
            ));
        }
    }



    @PostMapping("/progress")
    public ResponseEntity<Map<String, Object>> updateEducationProgress(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            Long lessonId = Long.valueOf(request.get("lessonId").toString());
            
            User user = userRepository.findById(userId).orElse(null);
            EducationLesson lesson = educationLessonRepository.findById(lessonId).orElse(null);
            
            if (user == null || lesson == null) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "User or lesson not found"
                ));
            }

            EducationProgress progress = educationProgressRepository
                .findByUserAndLesson(user, lesson)
                .orElse(new EducationProgress());

            progress.setUser(user);
            progress.setLesson(lesson);
            
            if (request.get("progressPercentage") != null) {
                progress.setProgressPercentage(Double.valueOf(request.get("progressPercentage").toString()));
            }
            
            if (request.get("timeSpentMinutes") != null) {
                progress.setTimeSpentMinutes(Integer.valueOf(request.get("timeSpentMinutes").toString()));
            }
            
            if (request.get("isCompleted") != null) {
                Boolean isCompleted = Boolean.valueOf(request.get("isCompleted").toString());
                progress.setIsCompleted(isCompleted);
                if (isCompleted) {
                    progress.setCompletedAt(LocalDateTime.now());
                    progress.setProgressPercentage(100.0);
                }
            }
            
            if (request.get("quizScore") != null) {
                progress.setQuizScore(Double.valueOf(request.get("quizScore").toString()));
            }
            
            if (request.get("notes") != null) {
                progress.setNotes(request.get("notes").toString());
            }

            progress.setLastAccessedAt(LocalDateTime.now());

            EducationProgress savedProgress = educationProgressRepository.save(progress);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Education progress updated successfully",
                "progress", savedProgress
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to update education progress: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/popular")
    public ResponseEntity<Map<String, Object>> getPopularLessons() {
        try {
            List<EducationLesson> popularLessons = educationLessonRepository.findMostPopularLessons();

            return ResponseEntity.ok(Map.of(
                "success", true,
                "popularLessons", popularLessons
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch popular lessons: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/search")
    public ResponseEntity<Map<String, Object>> searchLessons(@RequestParam String query) {
        try {
            List<EducationLesson> searchResults = educationLessonRepository.searchLessons(query);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "searchResults", searchResults
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to search lessons: " + e.getMessage()
            ));
        }
    }
}
