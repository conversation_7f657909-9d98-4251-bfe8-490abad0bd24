import 'dart:convert';
import 'package:http/http.dart' as http;
import '../constants/app_constants.dart';
import 'auth_service.dart';

enum StiTestType { hiv, syphilis, gonorrhea, chlamydia, hepatitisB, herpes, comprehensive }
enum TestResultStatus { negative, positive, inconclusive, pending }

class StiTestRecord {
  final String id;
  final String userId;
  final StiTestType testType;
  final DateTime testDate;
  final String? testLocation;
  final String? testProvider;
  final TestResultStatus resultStatus;
  final DateTime? resultDate;
  final bool followUpRequired;
  final DateTime? followUpDate;
  final String? notes;
  final bool isConfidential;
  final DateTime createdAt;
  final DateTime updatedAt;

  StiTestRecord({
    required this.id,
    required this.userId,
    required this.testType,
    required this.testDate,
    this.testLocation,
    this.testProvider,
    required this.resultStatus,
    this.resultDate,
    required this.followUpRequired,
    this.followUpDate,
    this.notes,
    required this.isConfidential,
    required this.createdAt,
    required this.updatedAt,
  });

  factory StiTestRecord.fromJson(Map<String, dynamic> json) {
    return StiTestRecord(
      id: json['id'].toString(),
      userId: json['user']['id'].toString(),
      testType: StiTestType.values.firstWhere(
        (e) => e.name.toUpperCase() == json['testType'].toString().toUpperCase().replaceAll('_', ''),
      ),
      testDate: DateTime.parse(json['testDate']),
      testLocation: json['testLocation'],
      testProvider: json['testProvider'],
      resultStatus: TestResultStatus.values.firstWhere(
        (e) => e.name.toUpperCase() == json['resultStatus'].toString().toUpperCase(),
      ),
      resultDate: json['resultDate'] != null ? DateTime.parse(json['resultDate']) : null,
      followUpRequired: json['followUpRequired'] ?? false,
      followUpDate: json['followUpDate'] != null ? DateTime.parse(json['followUpDate']) : null,
      notes: json['notes'],
      isConfidential: json['isConfidential'] ?? true,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'testType': testType.name.toUpperCase(),
      'testDate': testDate.toIso8601String().split('T')[0],
      'testLocation': testLocation,
      'testProvider': testProvider,
      'resultStatus': resultStatus.name.toUpperCase(),
      'resultDate': resultDate?.toIso8601String().split('T')[0],
      'followUpRequired': followUpRequired,
      'followUpDate': followUpDate?.toIso8601String().split('T')[0],
      'notes': notes,
      'isConfidential': isConfidential,
    };
  }
}

class StiTestRecordService {
  final String baseUrl = AppConstants.baseUrl;
  final AuthService _authService = AuthService();

  Future<String?> _getAuthToken() async {
    return _authService.currentToken;
  }

  Future<String?> _getCurrentUserId() async {
    final user = await _authService.getCurrentUser();
    return user?.id;
  }

  Map<String, String> _getHeaders(String? token) {
    final headers = {
      'Content-Type': 'application/json',
    };
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }

  /// Get STI test records
  Future<List<StiTestRecord>> getStiTestRecords({
    StiTestType? testType,
    TestResultStatus? resultStatus,
  }) async {
    try {
      final token = await _getAuthToken();
      final userId = await _getCurrentUserId();
      
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final queryParams = <String, String>{'userId': userId};
      if (testType != null) queryParams['testType'] = testType.name.toUpperCase();
      if (resultStatus != null) queryParams['resultStatus'] = resultStatus.name.toUpperCase();

      final uri = Uri.parse('$baseUrl/sti-test-records')
          .replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final List<dynamic> recordsJson = data['records'];
          return recordsJson.map((json) => StiTestRecord.fromJson(json)).toList();
        }
      }

      return [];
    } catch (e) {
      print('Error loading STI test records: $e');
      return [];
    }
  }

  /// Create STI test record
  Future<StiTestRecord?> createStiTestRecord({
    required StiTestType testType,
    required DateTime testDate,
    String? testLocation,
    String? testProvider,
    TestResultStatus? resultStatus,
    DateTime? resultDate,
    bool? followUpRequired,
    DateTime? followUpDate,
    String? notes,
    bool? isConfidential,
  }) async {
    try {
      final token = await _getAuthToken();
      final userId = await _getCurrentUserId();
      
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final requestData = {
        'userId': userId,
        'testType': testType.name.toUpperCase(),
        'testDate': testDate.toIso8601String().split('T')[0],
        'testLocation': testLocation,
        'testProvider': testProvider,
        'resultStatus': resultStatus?.name.toUpperCase() ?? 'PENDING',
        'resultDate': resultDate?.toIso8601String().split('T')[0],
        'followUpRequired': followUpRequired ?? false,
        'followUpDate': followUpDate?.toIso8601String().split('T')[0],
        'notes': notes,
        'isConfidential': isConfidential ?? true,
      };

      final uri = Uri.parse('$baseUrl/sti-test-records');
      final body = json.encode(requestData);

      final response = await http.post(
        uri,
        headers: _getHeaders(token),
        body: body,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return StiTestRecord.fromJson(data['record']);
        }
      }

      return null;
    } catch (e) {
      print('Error creating STI test record: $e');
      return null;
    }
  }

  /// Update STI test record
  Future<StiTestRecord?> updateStiTestRecord({
    required String recordId,
    String? testLocation,
    String? testProvider,
    TestResultStatus? resultStatus,
    DateTime? resultDate,
    bool? followUpRequired,
    DateTime? followUpDate,
    String? notes,
  }) async {
    try {
      final token = await _getAuthToken();

      final requestData = <String, dynamic>{};
      if (testLocation != null) requestData['testLocation'] = testLocation;
      if (testProvider != null) requestData['testProvider'] = testProvider;
      if (resultStatus != null) requestData['resultStatus'] = resultStatus.name.toUpperCase();
      if (resultDate != null) requestData['resultDate'] = resultDate.toIso8601String().split('T')[0];
      if (followUpRequired != null) requestData['followUpRequired'] = followUpRequired;
      if (followUpDate != null) requestData['followUpDate'] = followUpDate.toIso8601String().split('T')[0];
      if (notes != null) requestData['notes'] = notes;

      final uri = Uri.parse('$baseUrl/sti-test-records/$recordId');
      final body = json.encode(requestData);

      final response = await http.put(
        uri,
        headers: _getHeaders(token),
        body: body,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return StiTestRecord.fromJson(data['record']);
        }
      }

      return null;
    } catch (e) {
      print('Error updating STI test record: $e');
      return null;
    }
  }

  /// Delete STI test record
  Future<bool> deleteStiTestRecord(String recordId) async {
    try {
      final token = await _getAuthToken();
      final userId = await _getCurrentUserId();
      
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final uri = Uri.parse('$baseUrl/sti-test-records/$recordId?userId=$userId');

      final response = await http.delete(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['success'] == true;
      }

      return false;
    } catch (e) {
      print('Error deleting STI test record: $e');
      return false;
    }
  }

  /// Get follow-ups due
  Future<List<StiTestRecord>> getFollowUpsDue() async {
    try {
      final token = await _getAuthToken();
      final userId = await _getCurrentUserId();
      
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final uri = Uri.parse('$baseUrl/sti-test-records/follow-ups?userId=$userId');
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final List<dynamic> recordsJson = data['followUps'];
          return recordsJson.map((json) => StiTestRecord.fromJson(json)).toList();
        }
      }

      return [];
    } catch (e) {
      print('Error loading follow-ups: $e');
      return [];
    }
  }
}
