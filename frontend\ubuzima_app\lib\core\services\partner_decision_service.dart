import 'dart:convert';
import 'package:http/http.dart' as http;
import '../constants/app_constants.dart';
import 'auth_service.dart';

enum DecisionType { contraception, familyPlanning, healthGoal, lifestyle }
enum DecisionStatus { proposed, discussing, agreed, disagreed, postponed }

class PartnerDecision {
  final String id;
  final String userId;
  final String? partnerId;
  final DecisionType decisionType;
  final String decisionTitle;
  final String? decisionDescription;
  final DecisionStatus decisionStatus;
  final DateTime? targetDate;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  PartnerDecision({
    required this.id,
    required this.userId,
    this.partnerId,
    required this.decisionType,
    required this.decisionTitle,
    this.decisionDescription,
    required this.decisionStatus,
    this.targetDate,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory PartnerDecision.fromJson(Map<String, dynamic> json) {
    return PartnerDecision(
      id: json['id'].toString(),
      userId: json['user']['id'].toString(),
      partnerId: json['partner']?['id']?.toString(),
      decisionType: DecisionType.values.firstWhere(
        (e) => e.name.toUpperCase() == json['decisionType'].toString().toUpperCase(),
      ),
      decisionTitle: json['decisionTitle'],
      decisionDescription: json['decisionDescription'],
      decisionStatus: DecisionStatus.values.firstWhere(
        (e) => e.name.toUpperCase() == json['decisionStatus'].toString().toUpperCase(),
      ),
      targetDate: json['targetDate'] != null ? DateTime.parse(json['targetDate']) : null,
      notes: json['notes'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'partnerId': partnerId,
      'decisionType': decisionType.name.toUpperCase(),
      'decisionTitle': decisionTitle,
      'decisionDescription': decisionDescription,
      'decisionStatus': decisionStatus.name.toUpperCase(),
      'targetDate': targetDate?.toIso8601String().split('T')[0],
      'notes': notes,
    };
  }
}

class PartnerDecisionService {
  final String baseUrl = AppConstants.baseUrl;
  final AuthService _authService = AuthService();

  Future<String?> _getAuthToken() async {
    return _authService.currentToken;
  }

  Future<String?> _getCurrentUserId() async {
    final user = await _authService.getCurrentUser();
    return user?.id;
  }

  Map<String, String> _getHeaders(String? token) {
    final headers = {
      'Content-Type': 'application/json',
    };
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }

  /// Get partner decisions
  Future<List<PartnerDecision>> getPartnerDecisions({
    DecisionType? type,
    DecisionStatus? status,
  }) async {
    try {
      final token = await _getAuthToken();
      final userId = await _getCurrentUserId();
      
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final queryParams = <String, String>{'userId': userId};
      if (type != null) queryParams['type'] = type.name.toUpperCase();
      if (status != null) queryParams['status'] = status.name.toUpperCase();

      final uri = Uri.parse('$baseUrl/partner-decisions')
          .replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final List<dynamic> decisionsJson = data['decisions'];
          return decisionsJson.map((json) => PartnerDecision.fromJson(json)).toList();
        }
      }

      return [];
    } catch (e) {
      print('Error loading partner decisions: $e');
      return [];
    }
  }

  /// Create partner decision
  Future<PartnerDecision?> createPartnerDecision({
    required DecisionType decisionType,
    required String decisionTitle,
    String? decisionDescription,
    String? partnerId,
    DateTime? targetDate,
    String? notes,
  }) async {
    try {
      final token = await _getAuthToken();
      final userId = await _getCurrentUserId();
      
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final requestData = {
        'userId': userId,
        'decisionType': decisionType.name.toUpperCase(),
        'decisionTitle': decisionTitle,
        'decisionDescription': decisionDescription,
        'partnerId': partnerId,
        'targetDate': targetDate?.toIso8601String().split('T')[0],
        'notes': notes,
      };

      final uri = Uri.parse('$baseUrl/partner-decisions');
      final body = json.encode(requestData);

      final response = await http.post(
        uri,
        headers: _getHeaders(token),
        body: body,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return PartnerDecision.fromJson(data['decision']);
        }
      }

      return null;
    } catch (e) {
      print('Error creating partner decision: $e');
      return null;
    }
  }

  /// Update partner decision
  Future<PartnerDecision?> updatePartnerDecision({
    required String decisionId,
    String? decisionTitle,
    String? decisionDescription,
    DecisionStatus? decisionStatus,
    DateTime? targetDate,
    String? notes,
  }) async {
    try {
      final token = await _getAuthToken();

      final requestData = <String, dynamic>{};
      if (decisionTitle != null) requestData['decisionTitle'] = decisionTitle;
      if (decisionDescription != null) requestData['decisionDescription'] = decisionDescription;
      if (decisionStatus != null) requestData['decisionStatus'] = decisionStatus.name.toUpperCase();
      if (targetDate != null) requestData['targetDate'] = targetDate.toIso8601String().split('T')[0];
      if (notes != null) requestData['notes'] = notes;

      final uri = Uri.parse('$baseUrl/partner-decisions/$decisionId');
      final body = json.encode(requestData);

      final response = await http.put(
        uri,
        headers: _getHeaders(token),
        body: body,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return PartnerDecision.fromJson(data['decision']);
        }
      }

      return null;
    } catch (e) {
      print('Error updating partner decision: $e');
      return null;
    }
  }

  /// Delete partner decision
  Future<bool> deletePartnerDecision(String decisionId) async {
    try {
      final token = await _getAuthToken();
      final userId = await _getCurrentUserId();
      
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final uri = Uri.parse('$baseUrl/partner-decisions/$decisionId?userId=$userId');

      final response = await http.delete(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['success'] == true;
      }

      return false;
    } catch (e) {
      print('Error deleting partner decision: $e');
      return false;
    }
  }

  /// Get shared decisions (decisions involving the user as partner)
  Future<List<PartnerDecision>> getSharedDecisions() async {
    try {
      final token = await _getAuthToken();
      final userId = await _getCurrentUserId();
      
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final uri = Uri.parse('$baseUrl/partner-decisions/shared?userId=$userId');
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final List<dynamic> decisionsJson = data['decisions'];
          return decisionsJson.map((json) => PartnerDecision.fromJson(json)).toList();
        }
      }

      return [];
    } catch (e) {
      print('Error loading shared decisions: $e');
      return [];
    }
  }
}
