import 'dart:convert';
import 'package:http/http.dart' as http;
import '../constants/app_constants.dart';
import 'auth_service.dart';

enum SettingCategory { general, privacy, notifications, appearance }

class UserSettingsService {
  final String baseUrl = AppConstants.baseUrl;
  final AuthService _authService = AuthService();

  Future<String?> _getAuthToken() async {
    return _authService.currentToken;
  }

  Map<String, String> _getHeaders(String? token) {
    final headers = {
      'Content-Type': 'application/json',
    };
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }

  /// Get user settings by category
  Future<Map<String, dynamic>> getUserSettings({SettingCategory? category}) async {
    try {
      final token = await _getAuthToken();
      final queryParams = category != null 
          ? {'category': category.name.toUpperCase()}
          : <String, String>{};

      final uri = Uri.parse('$baseUrl/user-settings')
          .replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return data['settings'] ?? {};
        }
      }

      return _getDefaultSettings();
    } catch (e) {
      print('Error loading user settings: $e');
      return _getDefaultSettings();
    }
  }

  /// Save user settings
  Future<bool> saveUserSettings(Map<String, dynamic> settings) async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/user-settings');
      
      final body = json.encode(settings);

      final response = await http.post(
        uri,
        headers: _getHeaders(token),
        body: body,
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error saving user settings: $e');
      return false;
    }
  }

  /// Update settings for a specific category
  Future<bool> updateCategorySettings(SettingCategory category, Map<String, dynamic> settings) async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/user-settings/${category.name.toLowerCase()}');
      
      final body = json.encode(settings);

      final response = await http.put(
        uri,
        headers: _getHeaders(token),
        body: body,
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error updating category settings: $e');
      return false;
    }
  }

  /// Save general settings
  Future<bool> saveGeneralSettings({
    String? language,
    String? theme,
    bool? enableVoiceCommands,
    bool? enableOfflineMode,
    bool? autoSync,
    int? syncInterval,
  }) async {
    final settings = <String, dynamic>{};
    
    if (language != null) settings['language'] = language;
    if (theme != null) settings['theme'] = theme;
    if (enableVoiceCommands != null) settings['enableVoiceCommands'] = enableVoiceCommands;
    if (enableOfflineMode != null) settings['enableOfflineMode'] = enableOfflineMode;
    if (autoSync != null) settings['autoSync'] = autoSync;
    if (syncInterval != null) settings['syncInterval'] = syncInterval;

    return updateCategorySettings(SettingCategory.general, settings);
  }

  /// Save privacy settings
  Future<bool> savePrivacySettings({
    bool? shareHealthData,
    bool? allowDataAnalytics,
    bool? shareLocationData,
    bool? allowPersonalizedAds,
    bool? shareUsageStatistics,
    bool? allowThirdPartyAccess,
    bool? enableDataEncryption,
    bool? requireBiometricAuth,
    bool? enableTwoFactorAuth,
    bool? logSecurityEvents,
    String? dataRetentionPeriod,
    String? backupFrequency,
  }) async {
    final settings = <String, dynamic>{};
    
    if (shareHealthData != null) settings['shareHealthData'] = shareHealthData;
    if (allowDataAnalytics != null) settings['allowDataAnalytics'] = allowDataAnalytics;
    if (shareLocationData != null) settings['shareLocationData'] = shareLocationData;
    if (allowPersonalizedAds != null) settings['allowPersonalizedAds'] = allowPersonalizedAds;
    if (shareUsageStatistics != null) settings['shareUsageStatistics'] = shareUsageStatistics;
    if (allowThirdPartyAccess != null) settings['allowThirdPartyAccess'] = allowThirdPartyAccess;
    if (enableDataEncryption != null) settings['enableDataEncryption'] = enableDataEncryption;
    if (requireBiometricAuth != null) settings['requireBiometricAuth'] = requireBiometricAuth;
    if (enableTwoFactorAuth != null) settings['enableTwoFactorAuth'] = enableTwoFactorAuth;
    if (logSecurityEvents != null) settings['logSecurityEvents'] = logSecurityEvents;
    if (dataRetentionPeriod != null) settings['dataRetentionPeriod'] = dataRetentionPeriod;
    if (backupFrequency != null) settings['backupFrequency'] = backupFrequency;

    return updateCategorySettings(SettingCategory.privacy, settings);
  }

  /// Save notification settings
  Future<bool> saveNotificationSettings({
    bool? enablePushNotifications,
    bool? enableEmailNotifications,
    bool? enableSMSNotifications,
    bool? healthReminders,
    bool? appointmentReminders,
    bool? medicationReminders,
    bool? educationalContent,
    bool? emergencyAlerts,
    bool? partnerUpdates,
    bool? systemUpdates,
    String? quietHoursStart,
    String? quietHoursEnd,
    String? reminderFrequency,
    bool? enableVibration,
    bool? enableSound,
    String? notificationTone,
  }) async {
    final settings = <String, dynamic>{};
    
    if (enablePushNotifications != null) settings['enablePushNotifications'] = enablePushNotifications;
    if (enableEmailNotifications != null) settings['enableEmailNotifications'] = enableEmailNotifications;
    if (enableSMSNotifications != null) settings['enableSMSNotifications'] = enableSMSNotifications;
    if (healthReminders != null) settings['healthReminders'] = healthReminders;
    if (appointmentReminders != null) settings['appointmentReminders'] = appointmentReminders;
    if (medicationReminders != null) settings['medicationReminders'] = medicationReminders;
    if (educationalContent != null) settings['educationalContent'] = educationalContent;
    if (emergencyAlerts != null) settings['emergencyAlerts'] = emergencyAlerts;
    if (partnerUpdates != null) settings['partnerUpdates'] = partnerUpdates;
    if (systemUpdates != null) settings['systemUpdates'] = systemUpdates;
    if (quietHoursStart != null) settings['quietHoursStart'] = quietHoursStart;
    if (quietHoursEnd != null) settings['quietHoursEnd'] = quietHoursEnd;
    if (reminderFrequency != null) settings['reminderFrequency'] = reminderFrequency;
    if (enableVibration != null) settings['enableVibration'] = enableVibration;
    if (enableSound != null) settings['enableSound'] = enableSound;
    if (notificationTone != null) settings['notificationTone'] = notificationTone;

    return updateCategorySettings(SettingCategory.notifications, settings);
  }

  /// Save appearance settings
  Future<bool> saveAppearanceSettings({
    String? theme,
    String? primaryColor,
    String? accentColor,
    double? fontSize,
    String? fontFamily,
    bool? enableAnimations,
    bool? enableHapticFeedback,
    String? dashboardLayout,
    bool? showHealthTips,
    bool? compactMode,
  }) async {
    final settings = <String, dynamic>{};
    
    if (theme != null) settings['theme'] = theme;
    if (primaryColor != null) settings['primaryColor'] = primaryColor;
    if (accentColor != null) settings['accentColor'] = accentColor;
    if (fontSize != null) settings['fontSize'] = fontSize;
    if (fontFamily != null) settings['fontFamily'] = fontFamily;
    if (enableAnimations != null) settings['enableAnimations'] = enableAnimations;
    if (enableHapticFeedback != null) settings['enableHapticFeedback'] = enableHapticFeedback;
    if (dashboardLayout != null) settings['dashboardLayout'] = dashboardLayout;
    if (showHealthTips != null) settings['showHealthTips'] = showHealthTips;
    if (compactMode != null) settings['compactMode'] = compactMode;

    return updateCategorySettings(SettingCategory.appearance, settings);
  }

  /// Reset settings to default
  Future<bool> resetSettings({SettingCategory? category}) async {
    final defaultSettings = _getDefaultSettings();
    
    if (category != null) {
      final categorySettings = defaultSettings[category.name] ?? {};
      return updateCategorySettings(category, categorySettings);
    } else {
      return saveUserSettings(defaultSettings);
    }
  }

  /// Get default settings
  Map<String, dynamic> _getDefaultSettings() {
    return {
      'general': {
        'language': 'rw',
        'theme': 'system',
        'enableVoiceCommands': true,
        'enableOfflineMode': true,
        'autoSync': true,
        'syncInterval': 300, // 5 minutes
      },
      'privacy': {
        'shareHealthData': false,
        'allowDataAnalytics': true,
        'shareLocationData': false,
        'allowPersonalizedAds': false,
        'shareUsageStatistics': true,
        'allowThirdPartyAccess': false,
        'enableDataEncryption': true,
        'requireBiometricAuth': false,
        'enableTwoFactorAuth': false,
        'logSecurityEvents': true,
        'dataRetentionPeriod': '2_years',
        'backupFrequency': 'weekly',
      },
      'notifications': {
        'enablePushNotifications': true,
        'enableEmailNotifications': true,
        'enableSMSNotifications': false,
        'healthReminders': true,
        'appointmentReminders': true,
        'medicationReminders': true,
        'educationalContent': true,
        'emergencyAlerts': true,
        'partnerUpdates': true,
        'systemUpdates': true,
        'quietHoursStart': '22:00',
        'quietHoursEnd': '07:00',
        'reminderFrequency': 'daily',
        'enableVibration': true,
        'enableSound': true,
        'notificationTone': 'default',
      },
      'appearance': {
        'theme': 'system',
        'primaryColor': '#2E7D32',
        'accentColor': '#4CAF50',
        'fontSize': 16.0,
        'fontFamily': 'system',
        'enableAnimations': true,
        'enableHapticFeedback': true,
        'dashboardLayout': 'grid',
        'showHealthTips': true,
        'compactMode': false,
      },
    };
  }

  /// Export settings for backup
  Future<Map<String, dynamic>?> exportSettings() async {
    try {
      return await getUserSettings();
    } catch (e) {
      print('Error exporting settings: $e');
      return null;
    }
  }

  /// Import settings from backup
  Future<bool> importSettings(Map<String, dynamic> settings) async {
    try {
      return await saveUserSettings(settings);
    } catch (e) {
      print('Error importing settings: $e');
      return false;
    }
  }
}
