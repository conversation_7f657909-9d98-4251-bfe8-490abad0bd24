import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/theme/app_theme.dart';
import '../../core/services/notification_service.dart';
import '../../core/models/notification_model.dart';
import '../../widgets/voice_button.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final NotificationService _notificationService = NotificationService();
  List<NotificationItem> _notifications = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadNotifications();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadNotifications() async {
    setState(() => _isLoading = true);

    try {
      _notifications = await _notificationService.getNotifications();
    } catch (e) {
      _showErrorSnackBar('Habaye ikosa mu gushaka amamenyo');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: AppTheme.errorColor),
    );
  }

  void _handleVoiceCommand(String command) {
    final lowerCommand = command.toLowerCase();
    if (lowerCommand.contains('gusoma') || lowerCommand.contains('read')) {
      _markAllAsRead();
    } else if (lowerCommand.contains('gusiba') ||
        lowerCommand.contains('delete')) {
      _clearAllNotifications();
    }
  }

  void _markAllAsRead() async {
    try {
      await _notificationService.markAllAsRead();
      await _loadNotifications();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Amamenyo yose yasomwe'),
            backgroundColor: AppTheme.successColor,
          ),
        );
      }
    } catch (e) {
      _showErrorSnackBar('Habaye ikosa mu gusoma amamenyo');
    }
  }

  void _clearAllNotifications() async {
    try {
      await _notificationService.clearAllNotifications();
      await _loadNotifications();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Amamenyo yose yasibwe'),
            backgroundColor: AppTheme.successColor,
          ),
        );
      }
    } catch (e) {
      _showErrorSnackBar('Habaye ikosa mu gusiba amamenyo');
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width > 600;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          'Amamenyo',
          style: AppTheme.headingMedium.copyWith(
            color: Colors.white,
            fontSize: isTablet ? 24 : 20,
          ),
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(Icons.mark_email_read_rounded),
            onPressed: _markAllAsRead,
            tooltip: 'Soma byose',
          ),
          IconButton(
            icon: Icon(Icons.clear_all_rounded),
            onPressed: _clearAllNotifications,
            tooltip: 'Siba byose',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: [
            Tab(text: 'Byose'),
            Tab(text: 'Ubuzima'),
            Tab(text: 'Gahunda'),
            Tab(text: 'Ubutumwa'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildNotificationsList(_notifications, isTablet),
          _buildNotificationsList(
            _notifications
                .where((n) => n.type == NotificationType.health)
                .toList(),
            isTablet,
          ),
          _buildNotificationsList(
            _notifications
                .where((n) => n.type == NotificationType.appointment)
                .toList(),
            isTablet,
          ),
          _buildNotificationsList(
            _notifications
                .where((n) => n.type == NotificationType.message)
                .toList(),
            isTablet,
          ),
        ],
      ),
      floatingActionButton: VoiceButton(
        prompt: 'Vuga icyo ushaka gukora ku mamenyo - urugero: "Soma byose"',
        onResult: _handleVoiceCommand,
        tooltip: 'Koresha ijwi',
      ),
    );
  }

  Widget _buildNotificationsList(
    List<NotificationItem> notifications,
    bool isTablet,
  ) {
    if (_isLoading) {
      return Center(child: CircularProgressIndicator());
    }

    if (notifications.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.notifications_none_rounded,
              size: isTablet ? 80 : 64,
              color: AppTheme.textTertiary,
            ),
            SizedBox(height: AppTheme.spacing16),
            Text(
              'Nta mamenyo',
              style: AppTheme.headingSmall.copyWith(
                color: AppTheme.textTertiary,
              ),
            ),
            SizedBox(height: AppTheme.spacing8),
            Text(
              'Uzabona amamenyo hano iyo hari',
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(
        isTablet ? AppTheme.spacing24 : AppTheme.spacing16,
      ),
      itemCount: notifications.length,
      itemBuilder: (context, index) {
        final notification = notifications[index];
        return _buildNotificationCard(notification, isTablet, index);
      },
    );
  }

  Widget _buildNotificationCard(
    NotificationItem notification,
    bool isTablet,
    int index,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: AppTheme.spacing12),
      decoration: BoxDecoration(
        color:
            notification.isRead
                ? AppTheme.surfaceColor
                : AppTheme.primaryColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
        boxShadow: AppTheme.softShadow,
        border:
            notification.isRead
                ? null
                : Border.all(
                  color: AppTheme.primaryColor.withValues(alpha: 0.2),
                ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _markAsRead(notification),
          borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
          child: Padding(
            padding: EdgeInsets.all(
              isTablet ? AppTheme.spacing20 : AppTheme.spacing16,
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: isTablet ? 48 : 40,
                  height: isTablet ? 48 : 40,
                  decoration: BoxDecoration(
                    color: _getNotificationColor(
                      notification.type,
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(isTablet ? 24 : 20),
                  ),
                  child: Icon(
                    _getNotificationIcon(notification.type),
                    color: _getNotificationColor(notification.type),
                    size: isTablet ? 24 : 20,
                  ),
                ),
                SizedBox(width: AppTheme.spacing16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              notification.title,
                              style: AppTheme.labelLarge.copyWith(
                                fontWeight:
                                    notification.isRead
                                        ? FontWeight.normal
                                        : FontWeight.bold,
                              ),
                            ),
                          ),
                          if (!notification.isRead)
                            Container(
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                color: AppTheme.primaryColor,
                                shape: BoxShape.circle,
                              ),
                            ),
                        ],
                      ),
                      SizedBox(height: AppTheme.spacing4),
                      Text(
                        notification.message,
                        style: AppTheme.bodyMedium.copyWith(
                          color: AppTheme.textSecondary,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: AppTheme.spacing8),
                      Text(
                        _formatTime(notification.createdAt),
                        style: AppTheme.bodySmall.copyWith(
                          color: AppTheme.textTertiary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    ).animate(delay: (index * 100).ms).fadeIn().slideX(begin: 0.3);
  }

  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.health:
        return Icons.health_and_safety_rounded;
      case NotificationType.appointment:
        return Icons.calendar_today_rounded;
      case NotificationType.message:
        return Icons.chat_rounded;
      case NotificationType.system:
        return Icons.info_rounded;
      case NotificationType.reminder:
        return Icons.alarm_rounded;
    }
  }

  Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.health:
        return AppTheme.successColor;
      case NotificationType.appointment:
        return AppTheme.primaryColor;
      case NotificationType.message:
        return AppTheme.infoColor;
      case NotificationType.system:
        return AppTheme.warningColor;
      case NotificationType.reminder:
        return AppTheme.accentColor;
    }
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays} iminsi ishize';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} amasaha ashize';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} iminota ishize';
    } else {
      return 'Ubu';
    }
  }

  void _markAsRead(NotificationItem notification) async {
    if (!notification.isRead) {
      try {
        await _notificationService.markAsRead(notification.id);
        setState(() {
          notification.isRead = true;
        });
      } catch (e) {
        _showErrorSnackBar('Habaye ikosa mu gusoma uyu mamenyo');
      }
    }
  }
}
