import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';
import '../models/health_record_model.dart';

class HealthTrackingService {
  static final HealthTrackingService _instance =
      HealthTrackingService._internal();
  factory HealthTrackingService() => _instance;
  HealthTrackingService._internal();

  final String baseUrl = AppConstants.baseUrl;

  Future<String?> _getAuthToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('auth_token');
    } catch (e) {
      print('Error getting auth token: $e');
      return null;
    }
  }

  Future<String?> _getCurrentUserId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('user_id');
    } catch (e) {
      print('Error getting user ID: $e');
      return null;
    }
  }

  Map<String, String> _getHeaders([String? token]) {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }

  // Health Records
  Future<List<HealthRecord>> getHealthRecords({
    int page = 0,
    int limit = 10,
    String? type,
  }) async {
    try {
      final token = await _getAuthToken();
      final queryParams = {
        'page': page.toString(),
        'limit': limit.toString(),
        if (type != null) 'type': type,
      };

      final uri = Uri.parse(
        '$baseUrl/health-records',
      ).replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final List<dynamic> records = data['healthRecords'];
          return records.map((json) => HealthRecord.fromJson(json)).toList();
        }
      }

      throw Exception('Failed to load health records');
    } catch (e) {
      print('Error loading health records: $e');
      return [];
    }
  }

  Future<HealthRecord?> createHealthRecord(
    Map<String, dynamic> recordData,
  ) async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/health-records');

      final response = await http.post(
        uri,
        headers: _getHeaders(token),
        body: json.encode(recordData),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return HealthRecord.fromJson(data['healthRecord']);
        }
      }

      throw Exception('Failed to create health record');
    } catch (e) {
      print('Error creating health record: $e');
      return null;
    }
  }

  Future<Map<String, dynamic>?> getHealthStatistics({String? userId}) async {
    try {
      final token = await _getAuthToken();
      final queryParams = {if (userId != null) 'userId': userId};

      final uri = Uri.parse(
        '$baseUrl/health-records/statistics',
      ).replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return data['statistics'];
        }
      }

      return null;
    } catch (e) {
      print('Error loading health statistics: $e');
      return null;
    }
  }

  // Menstrual Cycle
  Future<List<Map<String, dynamic>>> getMenstrualCycles({
    String? userId,
    int page = 0,
    int limit = 10,
  }) async {
    try {
      final token = await _getAuthToken();
      final currentUserId = userId ?? await _getCurrentUserId();

      if (currentUserId == null) {
        throw Exception('User ID not found');
      }

      final queryParams = {
        'userId': currentUserId,
        'page': page.toString(),
        'limit': limit.toString(),
      };

      final uri = Uri.parse(
        '$baseUrl/menstrual-cycles',
      ).replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return List<Map<String, dynamic>>.from(data['cycles'] ?? []);
        }
      }

      throw Exception('Failed to load menstrual cycles');
    } catch (e) {
      print('Error loading menstrual cycles: $e');
      return [];
    }
  }

  Future<Map<String, dynamic>?> getCurrentCycle({String? userId}) async {
    try {
      final token = await _getAuthToken();
      final queryParams = {if (userId != null) 'userId': userId};

      final uri = Uri.parse(
        '$baseUrl/menstrual-cycles/current',
      ).replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return data['currentCycle'];
        }
      }

      return null;
    } catch (e) {
      print('Error loading current cycle: $e');
      return null;
    }
  }

  Future<Map<String, dynamic>?> getCyclePredictions({String? userId}) async {
    try {
      final token = await _getAuthToken();
      final queryParams = {if (userId != null) 'userId': userId};

      final uri = Uri.parse(
        '$baseUrl/menstrual-cycles/predictions',
      ).replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return data['predictions'];
        }
      }

      return null;
    } catch (e) {
      print('Error loading cycle predictions: $e');
      return null;
    }
  }

  Future<Map<String, dynamic>?> createMenstrualCycle(
    Map<String, dynamic> cycleData,
  ) async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/menstrual-cycles');

      final response = await http.post(
        uri,
        headers: _getHeaders(token),
        body: json.encode(cycleData),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return data['menstrualCycle'];
        }
      }

      throw Exception('Failed to create menstrual cycle');
    } catch (e) {
      print('Error creating menstrual cycle: $e');
      return null;
    }
  }

  Future<void> addHealthRecord(String type, Map<String, dynamic> data) async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/health-records');

      Map<String, dynamic> requestBody = {
        'type': type,
        'recordedAt': data['recordedAt'],
        'notes': data['notes'] ?? '',
      };

      // Handle different metric types
      switch (type) {
        case 'blood_pressure':
          requestBody['systolic'] = data['systolic'];
          requestBody['diastolic'] = data['diastolic'];
          requestBody['unit'] = data['unit'];
          break;
        default:
          requestBody['value'] = data['value'];
          requestBody['unit'] = data['unit'];
          break;
      }

      final body = json.encode(requestBody);

      final response = await http.post(
        uri,
        headers: _getHeaders(token),
        body: body,
      );

      if (response.statusCode != 201) {
        throw Exception('Failed to add health record');
      }
    } catch (e) {
      print('Error adding health record: $e');
      rethrow;
    }
  }

  // Medications
  Future<List<Map<String, dynamic>>> getMedications() async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/medications');
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return List<Map<String, dynamic>>.from(data['medications']);
        }
      }

      throw Exception('Failed to load medications');
    } catch (e) {
      print('Error loading medications: $e');
      return [];
    }
  }

  Future<List<Map<String, dynamic>>> getActiveMedications({
    String? userId,
  }) async {
    try {
      final token = await _getAuthToken();
      final currentUserId = userId ?? await _getCurrentUserId();

      if (currentUserId == null) {
        throw Exception('User ID not found');
      }

      final queryParams = {'userId': currentUserId};
      final uri = Uri.parse(
        '$baseUrl/medications/active',
      ).replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return List<Map<String, dynamic>>.from(
            data['activeMedications'] ?? [],
          );
        }
      }

      throw Exception('Failed to load active medications');
    } catch (e) {
      print('Error loading active medications: $e');
      return [];
    }
  }

  Future<Map<String, dynamic>?> createMedication(
    Map<String, dynamic> medicationData,
  ) async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/medications');

      final response = await http.post(
        uri,
        headers: _getHeaders(token),
        body: json.encode(medicationData),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return data['medication'];
        }
      }

      throw Exception('Failed to create medication');
    } catch (e) {
      print('Error creating medication: $e');
      return null;
    }
  }
}
