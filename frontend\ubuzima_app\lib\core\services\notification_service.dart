import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import '../models/notification_model.dart';
import '../constants/app_constants.dart';
import 'auth_service.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  FirebaseMessaging? _messaging;
  bool _isInitialized = false;

  // Initialize the notification service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _messaging = FirebaseMessaging.instance;

      // Request permission for notifications
      await _requestPermissions();

      // Set up message handlers
      _setupMessageHandlers();

      _isInitialized = true;
      debugPrint('NotificationService initialized successfully');
    } catch (e) {
      debugPrint('Error initializing NotificationService: $e');
    }
  }

  // Request notification permissions
  Future<void> _requestPermissions() async {
    if (_messaging == null) return;

    final settings = await _messaging!.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: false,
    );

    debugPrint(
      'Notification permission status: ${settings.authorizationStatus}',
    );
  }

  // Set up Firebase message handlers
  void _setupMessageHandlers() {
    if (_messaging == null) return;

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);

    // Handle notification taps
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);
  }

  // Handle foreground messages
  void _handleForegroundMessage(RemoteMessage message) {
    debugPrint('Received foreground message: ${message.messageId}');
    // In a real app, you might show a local notification here
  }

  // Handle notification taps
  void _handleNotificationTap(RemoteMessage message) {
    debugPrint('Notification tapped: ${message.messageId}');
    _navigateBasedOnNotification(message.data);
  }

  // Navigate based on notification data
  void _navigateBasedOnNotification(Map<String, dynamic> data) {
    final type = data['type'] as String?;

    switch (type) {
      case 'medication_reminder':
        // Navigate to medication screen
        break;
      case 'appointment_reminder':
        // Navigate to appointments screen
        break;
      case 'cycle_tracking':
        // Navigate to cycle tracking screen
        break;
      default:
        // Navigate to home screen
        break;
    }
  }

  // Simplified notification methods (stubs for now)
  Future<void> scheduleMedicationReminder({
    required String medicationName,
    required String dosage,
    required DateTime scheduledTime,
  }) async {
    debugPrint(
      'Medication reminder scheduled for $medicationName at $scheduledTime',
    );
    // In a real implementation, this would schedule a local notification
  }

  Future<void> scheduleAppointmentReminder({
    required DateTime appointmentTime,
    required String healthWorkerName,
    required String facilityName,
  }) async {
    debugPrint('Appointment reminder scheduled for $appointmentTime');
    // In a real implementation, this would schedule a local notification
  }

  Future<void> scheduleCycleReminder({
    required String title,
    required String body,
    required DateTime reminderDate,
  }) async {
    debugPrint('Cycle reminder scheduled: $title');
    // In a real implementation, this would schedule a local notification
  }

  Future<void> showEmergencyAlert({
    required String title,
    required String body,
  }) async {
    debugPrint('Emergency alert: $title - $body');
    // In a real implementation, this would show an immediate notification
  }

  // Get FCM token for push notifications
  Future<String?> getToken() async {
    if (_messaging == null) return null;

    try {
      return await _messaging!.getToken();
    } catch (e) {
      debugPrint('Error getting FCM token: $e');
      return null;
    }
  }

  // Subscribe to topic
  Future<void> subscribeToTopic(String topic) async {
    if (_messaging == null) return;

    try {
      await _messaging!.subscribeToTopic(topic);
      debugPrint('Subscribed to topic: $topic');
    } catch (e) {
      debugPrint('Error subscribing to topic $topic: $e');
    }
  }

  // Unsubscribe from topic
  Future<void> unsubscribeFromTopic(String topic) async {
    if (_messaging == null) return;

    try {
      await _messaging!.unsubscribeFromTopic(topic);
      debugPrint('Unsubscribed from topic: $topic');
    } catch (e) {
      debugPrint('Error unsubscribing from topic $topic: $e');
    }
  }

  // Cancel all notifications (stub)
  Future<void> cancelAllNotifications() async {
    debugPrint('All notifications cancelled');
    // In a real implementation, this would cancel all local notifications
  }

  // Cancel specific notification (stub)
  Future<void> cancelNotification(int id) async {
    debugPrint('Notification $id cancelled');
    // In a real implementation, this would cancel a specific local notification
  }

  // New methods for notification screen
  final String baseUrl = AppConstants.baseUrl;
  final AuthService _authService = AuthService();

  Future<String?> _getAuthToken() async {
    return _authService.currentToken;
  }

  Map<String, String> _getHeaders(String? token) {
    final headers = {'Content-Type': 'application/json'};
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }

  Future<List<NotificationItem>> getNotifications({
    int page = 0,
    int limit = 50,
    NotificationType? type,
    bool? isRead,
  }) async {
    try {
      final token = await _getAuthToken();
      final queryParams = {
        'page': page.toString(),
        'limit': limit.toString(),
        if (type != null) 'type': type.name,
        if (isRead != null) 'isRead': isRead.toString(),
      };

      final uri = Uri.parse(
        '$baseUrl/notifications',
      ).replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final List<dynamic> notifications = data['notifications'] ?? [];
          return notifications
              .map((json) => NotificationItem.fromJson(json))
              .toList();
        }
      }

      // Return sample data if API fails
      return _getSampleNotifications();
    } catch (e) {
      debugPrint('Error loading notifications: $e');
      return _getSampleNotifications();
    }
  }

  Future<void> markAsRead(String notificationId) async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/notifications/$notificationId/read');

      final response = await http.put(uri, headers: _getHeaders(token));

      if (response.statusCode != 200) {
        throw Exception('Failed to mark notification as read');
      }
    } catch (e) {
      debugPrint('Error marking notification as read: $e');
      rethrow;
    }
  }

  Future<void> markAllAsRead() async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/notifications/mark-all-read');

      final response = await http.put(uri, headers: _getHeaders(token));

      if (response.statusCode != 200) {
        throw Exception('Failed to mark all notifications as read');
      }
    } catch (e) {
      debugPrint('Error marking all notifications as read: $e');
      rethrow;
    }
  }

  Future<void> clearAllNotifications() async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/notifications');

      final response = await http.delete(uri, headers: _getHeaders(token));

      if (response.statusCode != 200) {
        throw Exception('Failed to clear notifications');
      }
    } catch (e) {
      debugPrint('Error clearing notifications: $e');
      rethrow;
    }
  }

  Future<int> getUnreadCount() async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/notifications/unread-count');

      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return data['count'] ?? 0;
        }
      }

      return 0;
    } catch (e) {
      debugPrint('Error getting unread count: $e');
      return 0;
    }
  }

  List<NotificationItem> _getSampleNotifications() {
    return [
      NotificationItem(
        id: '1',
        title: 'Wibutse gufata imiti yawe',
        message: 'Ni igihe cyo gufata imiti yawe ya saa 8:00',
        type: NotificationType.reminder,
        isRead: false,
        createdAt: DateTime.now().subtract(Duration(minutes: 30)),
      ),
      NotificationItem(
        id: '2',
        title: 'Gahunda yawe yo ejo',
        message: 'Ufite gahunda yo kubona umuganga ejo saa 10:00',
        type: NotificationType.appointment,
        isRead: false,
        createdAt: DateTime.now().subtract(Duration(hours: 2)),
      ),
      NotificationItem(
        id: '3',
        title: 'Ubutumwa bushya',
        message: 'Umuganga wawe yagukohereje ubutumwa',
        type: NotificationType.message,
        isRead: true,
        createdAt: DateTime.now().subtract(Duration(hours: 5)),
      ),
      NotificationItem(
        id: '4',
        title: 'Raporo y\'ubuzima',
        message: 'Raporo yawe y\'uku kwezi iraboneka',
        type: NotificationType.health,
        isRead: true,
        createdAt: DateTime.now().subtract(Duration(days: 1)),
      ),
      NotificationItem(
        id: '5',
        title: 'Kuvugurura sisitemu',
        message: 'Sisitemu izavugururwa ejo mu gitondo',
        type: NotificationType.system,
        isRead: false,
        createdAt: DateTime.now().subtract(Duration(days: 2)),
      ),
    ];
  }
}

// Background message handler (must be top-level function)
Future<void> _handleBackgroundMessage(RemoteMessage message) async {
  debugPrint('Received background message: ${message.messageId}');
  // Handle background message processing
}
